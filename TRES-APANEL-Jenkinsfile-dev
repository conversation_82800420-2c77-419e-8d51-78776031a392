pipeline {
    agent any
    // global env variables
    environment {

        EMAIL_RECIPIENTS = '<EMAIL>'
        PROJECT_CODE = 'TRES-BILLING-PANEL-dev-master'
        PROJECT_SRC = './'
        SONAR_HOST = 'https://sonar.devsparxit.com'
    }
    stages {
              stage('Code Upload') {

            steps {

                withCredentials([usernamePassword(credentialsId: 'f5364b10-1009-4dff-b0f2-3be5a016f19b', usernameVariable: 'DEVUSER', passwordVariable: 'DEVPASS')]) {
                    sh 'echo Deploying....'
                    sh 'git config git-ftp.url "ftp://*********:21/TRES-BILLING-PANEL"'
                    sh 'git config git-ftp.user ${DEVUSER}'
                    sh 'git config git-ftp.password ${DEVPASS}'
                    sh '/usr/local/bin/git-ftp init'
                }
            }
        }  
    

    
    }
    post {
        always {
            echo 'Post always';
        }
        success {
            echo 'send success mail';
        }
        unstable {
            echo 'send unstable mail';
        }
        failure {
            echo 'send failure mail';
        }
    }
}
