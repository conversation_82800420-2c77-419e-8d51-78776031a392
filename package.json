{"name": "tres-billing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "bootstrap": "^5.3.6", "crypto-js": "^4.2.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "sass": "^1.89.0", "signature_pad": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}