@use './assets/styles/mixin' as *;

.container{
  max-width: 1400px !important;
  width: 100%;
  margin: 0 auto;
  padding: 0 10px !important;

  @include for-size(tablet-view) {
    padding: 0 40px !important;
   }
}

h1{
    font-weight: 500 !important;
    font-size: 30px  !important;
}

.error , .required-star{
  color: #ff0000;
}

.optional-text{
  color: #B3B3B3;
  font-size: 16px;
}

.draft-button{
  font-weight: 600;
  color: #3C1A77;
  text-decoration: underline;
  background-color: transparent;
  border: none;
}

.download-icon{
  background: url(./assets/images/download.svg)no-repeat center;
  display: inline-block;
  width: 24px;
  height: 24px;
}

.right-arrow{
  background: url(./assets/images/right-white-arrow.svg)no-repeat center;
  display: inline-block;
  width: 24px;
  height: 24px;
}


.icon-block{
  background-color: rgba(24, 214, 220, 1);
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.transparent-btn{
  background: transparent;
  border: 1px solid #3C1A77;
  border-radius: 100px;
  color: #3C1A77;
  font-weight: 600;
  padding: 10px 30px;
  // min-width: 287px;


  @include for-size(tablet-view) {
    // width: 100%;
  // min-width: 287px;
   }
}

.purple-btn{
  background-color: #3C1A77;
  border-radius: 100px;
  color: #fff;
  font-weight: 600;
  padding: 10px 30px;
  display: flex;
  align-items: center;
  gap: 5px;
  

  @include for-size(tablet-view){
    // width: 100%;
    // min-width: 287px;
  }  
}


// ----------------------progress-bar----------

.form-progress-bar{
  display: flex;
  gap: 24px;
  align-items: center;

  .step-text{
    color: #fff;
  }

  .step-bar-block{
    display: flex;
    gap: 16px;
    .bar{
      display: block;
      width: 56px;
      height: 4px;
      background-color: rgba(255, 255, 255, 0.32);
      border-radius: 4px;

      &.active{
        background-color: #18D6DC;
      }
    }
  }
}


// ------------------------banner----------------

.header-banner{
  background: url("./assets/images/banner-background.png");
  min-height: 350px;
  padding-top: 57px;
}


// ---------------------------form--------------------------------

.form-card{
  margin-top: -239px; 
  
  .form-card-box{
    background-color: #fff;
    padding: 35px 40px;
    border-radius: 20px;

    .form-header{
      display: flex;
      justify-content: space-between;
     
      p{
        font-weight: 500;
        font-size: 30px;
      }

      
    }
  }

  .form-section-heading{
    position: relative;
    &::after{
      content: "";
    background-color: #B3B3B3;
    position: absolute;
    width: 96.2%;
    height: 1px;
    top: 17px;
    right: 0;
    left: 31px;
    z-index: 0;
    }

    .heading{
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 24px;
      z-index: 9999999;
      background-color: #fff;
      display: inline-block;
      position: relative;
      padding-right: 20px;
    }
  }

  .form-control , .form-select{
    background-color: #F2F2F2;
    border-radius: 50px;
    padding: 14.2px;
    outline: none;
    border: transparent;

    &:focus{
      border-color: transparent;
      background-color: #F2F2F2;
      box-shadow: none;
    }

   
  }

  // .dark-form-input{
  //   background-color: rgba(159, 159, 159, 0.32);
  //   color: rgba(159, 159, 159, 1);
  // }

  .file-box{
    border: solid 1px #B3B3B3;
    border-radius: 100px;
    padding: 0;
    color: #9F9F9F;
    font-weight: 500;
    font-size: 18px;
    position: relative;
    display: flex;
        align-items: center;
        gap: 14px;

    .attach-box{
      width: 30%;
      background-color: #3C1A77;
      border-radius: 100px;
      height: 100%;
      color: #fff;
      font-weight: 600;
      gap: 10px;
      padding: 13px 0;

      .file-icon{
        background: url('./assets/images/file-icon.svg');
        display: inline-block;
        width: 20px;
        height: 20px;
      }
    }

    .main-attach-element{
      opacity: 0;
      position: absolute;
      top: 0;
      width: 100%;
    }

    .inner-file-box{
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
    }

  }


  .instruction-box{
    padding: 16px;
    background-color: #F9F7FE;
    border-radius: 8px;
  }

  


  .side-border-design{
    position: relative;
    .side-border-after{
      width: 1px;
      background-color: rgba(179, 179, 179, 0.5);
      height: 100%;
      position: absolute;
      top: 0;
      left: -90px;
      &::after{
        content: "";
        width: 15px;
        height: 13px;
        background: linear-gradient(to bottom left, transparent 49.5%, rgba(179, 179, 179, 1) 50% calc(50% + 1px), transparent calc(50% + 1px)) right / 50% 100%, linear-gradient(to bottom right, transparent 49.5%, rgba(179, 179, 179, 1) 50% calc(50% + 1px), transparent calc(50% + 1px)) left / 50% 100%, linear-gradient(#fff) bottom / calc(100% - 2px) 6px;
        background-repeat: no-repeat;
        position: absolute;
        transform: rotate(90deg);
        top: 20px;
        left: -2px;
      }
    }
  }


  // -------------------page-0-------------------

  .page0-form-header{
    text-align: center;
    margin-bottom: 32px;
    h1{
      margin-bottom: 16px;
    }
  }

  .form-instruction-block{
    text-align: center;
    border-bottom: rgba(179, 179, 179, 1) solid 1px;


    .icon-wrap{
      .icon-info {
        max-width: 364px;
        font-weight: 500;
        font-size: 18px;
      }
    } 
    
    .grp-w9-icon{
      background: url('./assets/images/grp-w9.svg') no-repeat center;
    }

    .plan-icon{
      background: url('./assets/images/plan.svg') no-repeat center;
    }
    .cobra-icon{
      background: url('./assets/images/cobra.svg') no-repeat center;
    }
    .grp-bank-icon{
      background: url('./assets/images/grp-bank.svg') no-repeat center;
    }

    .grp-w9-icon ,.plan-icon,.cobra-icon,.grp-bank-icon{
      display: inline-block;
      width: 32px;
      height: 32px;
    }


    


  }

  .form0-footer-info{
    text-align: center;
    padding: 32px 0;
    margin-bottom: 8px;

    span{
      font-weight: 500;
      font-size: 18px;
    }
  }
}


// --------------------modal-------------------

.modal-content{
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
}

.alert-icon{
  background: url(./assets/images/alert.svg) no-repeat center;
  width: 48px;
  height: 48px;
  display: inline-block;
}
.modal-body{
  font-weight: 600;
  font-size: 20px;
}

.btn-close{
  display: none;
}

.modal-header , .modal-footer{
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}


