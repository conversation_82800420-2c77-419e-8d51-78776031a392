import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import './App.scss';
import './assets/styles/global.scss'
import 'bootstrap/dist/css/bootstrap.min.css';
import Intake from './pages/Intake';
import Header from './components/Header/Header';
// import Test from './pages/Test';

function App() {
  return (
    <Router>
      <Header/>
      <Routes>
        <Route path="/intake/:id" element={<Intake />} />
        {/* <Route path="/test" element={<Test/>} /> */}
      </Routes>
    </Router>
  );
}

export default App;
