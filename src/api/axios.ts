import axios from "axios";

const url = import.meta.env.VITE_API_URL

// Access process.env safely with type assertion

export const axiosInstance = axios.create({
  baseURL: url+import.meta.env.VITE_ADMINSERVICE+import.meta.env.VITE_API_V1
});

const clientId = import.meta.env.VITE_CLIENT_ID;

axiosInstance.interceptors.request.use(
  function (config) {
    config.headers.channel = "ADMIN";
    config.headers.clientId = clientId;
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);