import Step1 from "../FormsWrap/Step1/Step1"
import Step2 from "./Step2/Step2"
import Step3 from "./Step3/Step3"
import Step4 from "./Step4/Step4"

const FormsWarp = ({step, setStep}) => {
  return (
    <div>
      {step === "1" && <Step1 setStep={setStep}/>}
      {step === "2" && <Step2 setStep={setStep}/>}
      {step === "3" && <Step3 setStep={setStep}/>}
      {step === "4" && <Step4 setStep={setStep}/>}
    </div>
  )
}

export default FormsWarp
