import Step1 from "../FormsWrap/Step1/Step1";
import Step2 from "./Step2/Step2";
import Step3 from "./Step3/Step3";
import Step4 from "./Step4/Step4";
import axios from "axios";
import { useEffect, useState } from "react";
import FinalStep from "./FinalStep/FinalStep";
import { axiosInstance } from "../../api/axios";
import { decryptionObj, encryption } from "../../utils/Encrypt";
import endPoints from "../../api/endpoints";
import { notify } from "../../utils/NotifyToasts";
import { useParams } from "react-router-dom";

interface FormsWarpProps {
  setStep: (step: string) => void;
  step: string;
  setLoader: (loader: boolean) => void;
  isDraft: boolean;
}


const FormsWarp: React.FC<FormsWarpProps> = ({ isDraft, setLoader, step, setStep }) => {
  const [dropdowns, setDropdowns] = useState({});
  const [draftData, setDraftData] = useState({});
  const {id} = useParams();

  console.log("draftData" , draftData);
  

  useEffect(()=>{
    if(id){
      dropDownData();
      if(isDraft){
        fetchDraft();
      }
    }
  }, [id, isDraft])

  const fetchDraft = async () =>{
    const EncKey = await encryption(id || "");
    setLoader(true);
    axiosInstance
      .get(`${endPoints.draft}?group_key=${encodeURIComponent(EncKey || "")}`)
      .then(async (response) => {
        setLoader(false);
        if (response?.data?.code === 200) {
          const decryptedData = await decryptionObj(response?.data?.response);
          if(decryptedData){
            if(JSON.parse(decryptedData)?.status === "COMPLETED"){
              setStep("completed");
            }else{
              setDraftData(JSON.parse(decryptedData));
              setStep(JSON.parse(decryptedData)?.intake_page_number)
            }
          }
        }
        else{
          notify.errorToast(
            response?.data?.response ?? "Something went wrong!"
          );
        }
      })
      .catch((error) => {
        setLoader(false);
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  }
  
  const dropDownData = () => {
    axiosInstance
      .get(`${endPoints.dropDownData}`)
      .then(async (response) => {
        if (response?.data?.code === 200) {
          const decryptedData = await decryptionObj(response?.data?.response);
          if(decryptedData){
            setDropdowns(JSON.parse(decryptedData))
          }
        }
      })
      .catch((error) => {
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  };
  return (
    <div>
      {step === "1" && <Step1 setLoader={setLoader} dropdowns={dropdowns} draftData={draftData} setStep={setStep} />}
      {step === "2" && <Step2 setLoader={setLoader} draftData={draftData} setStep={setStep} />}
      {step === "3" && <Step3 setLoader={setLoader} dropdowns={dropdowns} draftData={draftData} setStep={setStep} />}
      {step === "4" && <Step4 setLoader={setLoader} setStep={setStep} draftData={draftData} />}
      {step === "completed" && <FinalStep />}
    </div>
  );
};

export default FormsWarp;
