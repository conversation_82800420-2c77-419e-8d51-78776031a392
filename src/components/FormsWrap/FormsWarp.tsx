import { axiosInstance } from "@/api/axios";
import Step1 from "../FormsWrap/Step1/Step1";
import Step2 from "./Step2/Step2";
import Step3 from "./Step3/Step3";
import Step4 from "./Step4/Step4";
import endPoints from "@/api/endpoints";
import axios from "axios";
import { notify } from "@/utils/NotifyToasts";
import { useEffect, useState } from "react";
import FinalStep from "./FinalStep/FinalStep";
import { decryptionObj, encryption } from "@/utils/Encrypt";

interface FormsWarpProps {
  setStep: (step: string) => void;
  step: string;
}


const FormsWarp: React.FC<FormsWarpProps> = ({ step, setStep }) => {
  const [dropdowns, setDropdowns] = useState({});
  const [draftData, setDraftData] = useState({});

  console.log("draftData" , draftData);
  

  useEffect(()=>{
    dropDownData();
    fetchDraft();
  }, [])

  const fetchDraft = async () =>{
    const EncKey = await encryption("12345");
    axiosInstance
      .get(`${endPoints.draft}?group_key=${encodeURIComponent(EncKey || "")}`)
      .then(async (response) => {
        if (response?.data?.code === 200) {
          const decryptedData = await decryptionObj(response?.data?.response);
          setDraftData(JSON.parse(decryptedData));
          setStep(JSON.parse(decryptedData)?.intake_page_number)
        }
        else{
          notify.errorToast(
            response?.data?.response ?? "Something went wrong!"
          );
        }
      })
      .catch((error) => {
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  }
  
  const dropDownData = () => {
    axiosInstance
      .get(`${endPoints.dropDownData}`)
      .then(async (response) => {
        if (response?.data?.code === 200) {
          const decryptedData = await decryptionObj(response?.data?.response);
          setDropdowns(JSON.parse(decryptedData))
        }
      })
      .catch((error) => {
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  };
  return (
    <div>
      {step === "1" && <Step1 dropdowns={dropdowns} draftData={draftData} setStep={setStep} />}
      {step === "2" && <Step2 draftData={draftData} setStep={setStep} />}
      {step === "3" && <Step3 dropdowns={dropdowns} setStep={setStep} />}
      {step === "4" && <Step4 setStep={setStep} />}
      {step === "completed" && <FinalStep />}
    </div>
  );
};

export default FormsWarp;
