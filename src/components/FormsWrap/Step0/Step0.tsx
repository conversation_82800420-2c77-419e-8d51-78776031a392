import React, { useState } from "react";
import { Modal } from 'react-bootstrap';

interface Step0Props {
  setStep: (step: string) => void;
}

const Step0: React.FC<Step0Props> = ({setStep}) => {

    const [show, setShow] = useState(false);

    const handleShow = () => setShow(true);
    const handleClose = () => setShow(false);

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box d-flex align-items-center justify-content-center flex-column">
            <div className="page0-form-header">
              <h1>Tres Health Group Intake Form</h1>
              <p>
                Thank you for choosing to partner with Tres Health for your
                group medical benefit needs.
              </p>
            </div>

            <p className="instruction-box mb-5">
              This form is designed to collect the necessary information we need
              to get your group implemented quickly and accurately. The form
              takes roughly 15-20 minutes to complete and will ask for basic
              group information, contacts, your previous medical plan (if
              applicable, banking information, etc).
            </p>

            <div className="form-instruction-block">
              <span className="fw-medium mb-4">
                To complete this form, you will need the following information
                readily available:
              </span>
              <div className="row">

                <div className="col-md-6 mb-5 d-flex align-items-center icon-wrap  justify-content-center flex-column">
                  <div className="icon-block">
                    <i className="grp-w9-icon"></i>
                  </div>{" "}
                  <span className="icon-info">Group W9</span>
                  <span className="icon-info opacity-0">Group W9</span>

                </div>

                <div className="col-md-6 mb-5 d-flex align-items-center icon-wrap justify-content-center flex-column">
                  <div className="icon-block">
                    <i className="plan-icon"></i>
                  </div>
                  <span className="icon-info">
                    Your Previous Medical Plan Legal Name
                  </span>
                  <span className="icon-info">(if previously self-funded)</span>
                </div>

                <div className="col-md-6 mb-5 d-flex align-items-center icon-wrap justify-content-center flex-column">
                  <div className="icon-block">
                    <i className="cobra-icon"></i>
                  </div>
                  <span className="icon-info">
                    Your COBRA Administrator Information{" "}
                  </span>
                  <span className="icon-info">
                    (if not administered by Tres Health)
                  </span>
                </div>

                <div className="col-md-6 mb-5 d-flex align-items-center icon-wrap justify-content-center flex-column">
                  <div className="icon-block">
                    <i className="grp-bank-icon"></i>
                  </div>
                  <span className="icon-info">
                    Group Bank Account Information
                  </span>
                  <span className="icon-info">(for premium payments)</span>
                </div>
              </div>
            </div>

            <div className="form0-footer-info">
              <p>
                Once you complete and submit this form, our team will review and
                reach out with any follow up questions or missing information.
              </p>
              <p>Thank you again and we look forward to serving you!</p>
              <span>“The Tres Implementation Team”</span>
            </div>

            <button className="purple-btn" onClick = {handleShow}>
              Proceed
              <i
                className="right-arrow
            "
              ></i>
            </button>
          </div>
        </div>
      </div>


      {/* --------------------modal------------------------------- */}

      <Modal show={show} onHide={handleClose} className="already-have-draft">
        <Modal.Header closeButton>
          <span className="icon-block"><i className="alert-icon"></i></span>
        </Modal.Header>
        <Modal.Body>You Already Have a Draft for This Form!</Modal.Body>
        <Modal.Footer>
        <div className="row w-100 d-flex">
                    <div className="gap-3 col-6 col-md-12 mb-3 mb-sm-0 d-flex align-items-center justify-content-center">
                      <button type="button" className="transparent-btn" onClick={()=>setStep("1")}>
                      Discard & Start Over
                      </button>
                      <button type="submit"  className="purple-btn" onClick={()=>setStep("1")}>
                      Continue with Draft
                      </button>
                    </div>
                  </div>
        </Modal.Footer>
      </Modal>

    </div>
  );
};

export default Step0;
