import { Form } from "react-bootstrap";
import "../../../App.scss";
import React, { useEffect, useState } from "react";
import { usaStates } from "../../../utils/states";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import axios from "axios";
import { encryption, encryptionObj } from "../../../utils/Encrypt";
import { axiosInstance } from "../../../api/axios";
import endPoints from "../../../api/endpoints";
import { notify } from "../../../utils/NotifyToasts";

interface Step1Props {
  setStep: (step: string) => void;
  dropdowns: any;
  draftData: any;
}

const Step1: React.FC<Step1Props> = ({dropdowns, draftData, setStep}) => {
  const [data, setData] = useState<any>({
    group_details: {
      group_legal_name: null,
      dba: null,
      name_to_appear_on_id_cards: null,
      date_company_established: null,
      main_phone_number: null,
      main_fax: null,
      total_eligible_employees: null,
      sic_code: null,
      tax_id: null,
    },
    physical_address: {
      address1: null,
      address2: null,
      city: null,
      state: null,
      zip_code: null,
    },
    is_mailing_same_as_physical: true,
    mailing_address: {
      address1: null,
      address2: null,
      city: null,
      state: null,
      zip_code: null,
    },
    has_previous_medical_plan: false,
    is_draft: false,
    previous_medical_plan_details: {
      did_offer_medical_plan: null,
      was_self_funded: false,
      previous_carrier: null,
      legal_plan_name_assigned: null,
    },
    client_w9: null,
  });

  console.log("data",data)

  const [dataError, setDataError] = useState({
    group_details: {
      group_legal_name: false,
      name_to_appear_on_id_cards: false,
      main_phone_number: false,
      main_phone_number_valid: false,
      total_eligible_employees: false,
      sic_code: false,
      tax_id: false,
    },
    physical_address: {
      address1: false,
      // address2: false,
      city: false,
      state: false,
      zip_code: false,
    },
    is_mailing_same_as_physical: false,
    mailing_address: {
      address1: false,
      address2: false,
      city: false,
      state: false,
      zip_code: false,
    },
    has_previous_medical_plan: false,
    is_draft: false,
    previous_medical_plan_details: {
      did_offer_medical_plan: false,
      was_self_funded: false,
      previous_carrier: false,
      legal_plan_name_assigned: false,
    },
    client_w9: false,
  });

  useEffect(()=>{
    if(draftData?.group_info){
      setData(draftData?.group_info)
    }
  }, [draftData])

  const handleNumericInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>  ) => {
    let value = e.target.value;
    // Use replace with regex to remove all spaces
    value = value.replace(/\s/g, "");

    // Ensure the value is numeric and no more than 10 digits
    if (value.length <= 10 && !isNaN(Number(value))) {
      return value; // Set the valid value
    }
  };

  const formatPostalCodeWithHyphen = (value: string) => {
    // Remove any non-digit characters
    const cleaned = value.replace(/\D/g, "");

    // If the cleaned length is 9 digits, insert a hyphen after the 5th digit
    if (cleaned.length === 9) {
      return cleaned.replace(/(\d{5})(\d{4})/, "$1-$2");
    }

    // Otherwise, return the cleaned value (without any formatting)
    return cleaned;
  };

  const formatDateToYMD = (dateStr: any) => {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = (`0${date.getMonth() + 1}`).slice(-2); // Month is 0-based, so we add 1
        const day = (`0${date.getDate()}`).slice(-2); // Add leading zero if necessary
        return `${year}-${month}-${day}`; // Return in YYYY-MM-DD format
    }
    return dateStr; // Return the original string if it's not a valid date
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    let valid = true;
    if(!data?.group_details?.group_legal_name){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        group_legal_name: true,
      },}))
      valid=false;
    }
    //

    if(!data?.group_details?.name_to_appear_on_id_cards){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        name_to_appear_on_id_cards: true,
      },}))
      valid=false;
    }

    if(!data?.group_details?.main_phone_number){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        main_phone_number: true,
      },}))
      valid=false;
    }

    if(data?.group_details?.main_phone_number?.length > 0 && data?.group_details?.main_phone_number?.length < 10){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        main_phone_number_valid: true,
      },}))
      valid=false;
    }

    if(!data?.group_details?.total_eligible_employees){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        total_eligible_employees: true,
      },}))
      valid=false;
    }

    if(!data?.group_details?.sic_code){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        sic_code: true,
      },}))
      valid=false;
    }

    if(!data?.group_details?.tax_id){
      setDataError((prev)=>({...prev, group_details: {
        ...prev.group_details,
        tax_id: true,
      },}))
      valid=false;
    }

    if(!data?.physical_address?.address1){
      setDataError((prev)=>({...prev, physical_address: {
        ...prev.physical_address,
        address1: true,
      },}))
      valid=false;
    }

    if(!data?.physical_address?.city){
      setDataError((prev)=>({...prev, physical_address: {
        ...prev.physical_address,
        city: true,
      },}))
      valid=false;
    }

    if(!data?.physical_address?.state){
      setDataError((prev)=>({...prev, physical_address: {
        ...prev.physical_address,
        state: true,
      },}))
      valid=false;
    }

    if(!data?.physical_address?.zip_code){
      setDataError((prev)=>({...prev, physical_address: {
        ...prev.physical_address,
        zip_code: true,
      },}))
      valid=false;
    }

    if(data?.is_mailing_same_as_physical === false){
      if(!data?.mailing_address?.address1){
        setDataError((prev)=>({...prev, mailing_address: {
          ...prev.mailing_address,
          address1: true,
        },}))
        valid=false;
      }
  
      if(!data?.mailing_address?.city){
        setDataError((prev)=>({...prev, mailing_address: {
          ...prev.mailing_address,
          city: true,
        },}))
        valid=false;
      }
  
      if(!data?.mailing_address?.state){
        setDataError((prev)=>({...prev, mailing_address: {
          ...prev.mailing_address,
          state: true,
        },}))
        valid=false;
      }
  
      if(!data?.mailing_address?.zip_code){
        setDataError((prev)=>({...prev, mailing_address: {
          ...prev.mailing_address,
          zip_code: true,
        },}))
        valid=false;
      }
    }

    if(data?.has_previous_medical_plan){
      if(data?.previous_medical_plan_details?.was_self_funded){
        if(!data?.previous_medical_plan_details?.legal_plan_name_assigned){
          setDataError((prev)=>({...prev, previous_medical_plan_details: {
            ...prev.previous_medical_plan_details,
            legal_plan_name_assigned: true,
          },}))
          valid=false;
        }
      }
      else if(!data?.previous_medical_plan_details?.previous_carrier){
          setDataError((prev)=>({...prev, previous_medical_plan_details: {
            ...prev.previous_medical_plan_details,
            previous_carrier: true,
          },}))
          valid=false;
        }
    }

    if(valid){
      const EncKey = await encryption("12345");
      // const convertToFormData = (data: any) => {
      //   const formData = new FormData();
      
      //   const appendFormData = (formData: FormData, data: any, parentKey: string | null = null) => {
      //     Object.entries(data).forEach(([key, value]) => {
      //       const formKey = parentKey ? `${parentKey}[${key}]` : key;
      
      //       if (value instanceof File) {
      //         formData.append(formKey, value);
      //       } else if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      //         appendFormData(formData, value, formKey);
      //       } else {
      //         formData.append(formKey, value ?? '');
      //       }
      //     });
      //   };
      
      //   appendFormData(formData, data);
      //   return formData;
      // };
      
      const formDataPayload = await encryptionObj(data);
      axiosInstance
      .post(`${endPoints.intakeSubmit}?group_key=${encodeURIComponent(EncKey || "")}&intake_page_number=1`, {data: formDataPayload})
      .then(async (response) => {
        if (response?.data?.code === 200) {
          setStep("2");
        }
      })
      .catch((error) => {
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
    }
  };

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <button className="draft-button">Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Group Details</span>
                </div>
                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Group Legal Name<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="group_legal_name"
                      id="group_legal_name"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={data.group_details.group_legal_name || ""}
                    ></Form.Control>
                    {dataError.group_details.group_legal_name && (
                      <p className="error">Please enter group legal name</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      DBA(s) <span className="optional-text">(Optional)</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="dba"
                      onChange={(e) =>
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }))
                      }
                      value={data.group_details.dba || ""}
                    ></Form.Control>
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Name to Appear on ID Cards
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="name_to_appear_on_id_cards"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={
                        data.group_details.name_to_appear_on_id_cards || ""
                      }
                    ></Form.Control>
                    {dataError.group_details.name_to_appear_on_id_cards && (
                      <p className="error">Please enter name</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Date Company Established{" "}
                      <span className="optional-text">(Optional)</span>
                    </Form.Label>
                    <div className="position-relative">
                      <DatePicker
                        showYearDropdown
                        scrollableYearDropdown
                        yearDropdownItemNumber={100}
                        selected={
                          data.group_details.date_company_established
                            ? new Date(
                                data.group_details.date_company_established
                              )
                            : null
                        }
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            group_details: {
                              ...prev.group_details,
                              date_company_established: formatDateToYMD(e),
                            },
                          }));
                        }}
                        dateFormat="MM-dd-yyyy"
                        className="form-control"
                        placeholderText="mm-dd-yyyy"
                        autoComplete="off"
                        onKeyDown={(e) => e.preventDefault()}
                      />
                      <i className="calender-icon"></i>
                    </div>
                    {/* <Form.Control
                      type="text"
                      name="date_company_established"
                      onChange={(e) =>
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }))
                      }
                      value={data.group_details.date_company_established || ""}
                    ></Form.Control> */}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Main Phone Number<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="main_phone_number"
                      maxLength={10}
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: handleNumericInputChange(e),
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                            main_phone_number_valid: false,
                          },
                        }));
                      }}
                      value={data.group_details.main_phone_number || ""}
                    ></Form.Control>
                    {dataError.group_details.main_phone_number && (
                      <p className="error">Please enter phone number</p>
                    )}
                    {dataError.group_details.main_phone_number_valid && (
                      <p className="error">Please enter a valid phone number</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Main Fax <span className="optional-text">(Optional)</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="main_fax"
                      onChange={(e) =>
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }))
                      }
                      value={data.group_details.main_fax || ""}
                    ></Form.Control>
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Total Eligible Employees
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="total_eligible_employees"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={data.group_details.total_eligible_employees || ""}
                    ></Form.Control>
                    {dataError.group_details.total_eligible_employees && (
                      <p className="error">Please enter total employees</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Industry (SIC Code)
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="sic_code"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={data.group_details.sic_code || ""}
                    >
                      {/* <i className="search-icon"></i> */}
                    </Form.Control>

                    {dataError.group_details.sic_code && (
                      <p className="error">Please enter SIC code</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Tax ID<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="tax_id"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          group_details: {
                            ...prev.group_details,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={data.group_details.tax_id || ""}
                    ></Form.Control>
                    {dataError.group_details.tax_id && (
                      <p className="error">Please enter tax id</p>
                    )}
                  </Form.Group>
                </div>
              </div>

              {/* -----------------physical-Address---------------------- */}

              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Physical Address</span>
                </div>
                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Address Line 1<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="address1"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                      value={data.physical_address.address1 || ""}
                    ></Form.Control>
                    {dataError.physical_address.address1 && (
                      <p className="error">Please enter address</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Address Line 2{" "}
                      <span className="optional-text">(Optional)</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      value={data.physical_address.address2 || ""}
                      name="address2"
                      onChange={(e) =>
                        setData((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: e.target.value,
                          },
                        }))
                      }
                    ></Form.Control>
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      City<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      value={data.physical_address.city || ""}
                      name="city"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                    ></Form.Control>
                    {dataError.physical_address.city && (
                      <p className="error">Please enter city</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group className="form-block mb-3">
                    <Form.Label htmlFor="State">
                      State <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Select
                      id="state"
                      aria-label="Default select example"
                      name="state"
                      value={data.physical_address.state || ""}
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: e.target.value,
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                    >
                      <option value="">Select State</option>
                      {usaStates.map((state) => (
                        <option
                          key={state.abbreviation}
                          value={state.abbreviation}
                        >
                          {state.name}
                        </option>
                      ))}
                    </Form.Select>
                    {dataError.physical_address.state && (
                      <p className="error">Please enter state</p>
                    )}
                  </Form.Group>
                </div>

                <div className="col-md-6 mb-3">
                  <Form.Group>
                    <Form.Label>
                      Zip Code<span className="required-star">*</span>
                    </Form.Label>
                    <Form.Control
                      type="text"
                      maxLength={10}
                      value={data.physical_address.zip_code || ""}
                      name="zip_code"
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: formatPostalCodeWithHyphen(
                              e.target.value
                            ),
                          },
                        }));
                        setDataError((prev: any) => ({
                          ...prev,
                          physical_address: {
                            ...prev.physical_address,
                            [e.target.name]: false,
                          },
                        }));
                      }}
                    ></Form.Control>
                    {dataError.physical_address.zip_code && (
                      <p className="error">Please enter zip code</p>
                    )}
                  </Form.Group>
                </div>
              </div>

              {/* ----------------Mailing Address----------------- */}

              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Mailing Address</span>
                </div>

                <p>
                  Is the Mailing Address same as the Physical Address?
                  <span className="required-star">*</span>
                </p>
                <Form.Group>
                  <div className="row">
                    <div className="col-3 col-md-1">
                      <Form.Check
                        type="radio"
                        label="Yes"
                        name="radioGroupAddress"
                        checked={data?.is_mailing_same_as_physical}
                        onChange={() =>
                          setData((prev: any) => ({
                            ...prev,
                            is_mailing_same_as_physical: true,
                            mailing_address: {},
                          }))
                        }
                        // value={data?.is_mailing_same_as_physical}
                      />
                    </div>

                    <div className="col-3 col-md-1">
                      <Form.Check
                        type="radio"
                        label="No"
                        name="radioGroupAddress"
                        checked={!data?.is_mailing_same_as_physical}
                        onChange={() =>
                          setData((prev: any) => ({
                            ...prev,
                            is_mailing_same_as_physical: false,
                          }))
                        }
                        // value={data?.is_mailing_same_as_physical}
                      />
                    </div>
                  </div>
                </Form.Group>

                {/* ----------------------No-case----------------- */}

                {data?.is_mailing_same_as_physical === false && (
                  <div className="mailing-no-case mt-3 row">
                    <div className="col-md-6 mb-3">
                      <Form.Group>
                        <Form.Label>
                          Address Line 1<span className="required-star">*</span>
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="address1"
                          onChange={(e) =>
                            setData((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: e.target.value,
                              },
                            }))
                          }
                          value={data.mailing_address.address1 || ""}
                        ></Form.Control>
                        {dataError.mailing_address.address1 && (
                          <p className="error">Please enter address</p>
                        )}
                      </Form.Group>
                    </div>

                    <div className="col-md-6 mb-3">
                      <Form.Group>
                        <Form.Label>
                          Address Line 2{" "}
                          <span className="optional-text">(Optional)</span>
                        </Form.Label>
                        <Form.Control
                          type="text"
                          value={data.mailing_address.address2 || ""}
                          name="address2"
                          onChange={(e) =>
                            setData((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: e.target.value,
                              },
                            }))
                          }
                        ></Form.Control>
                      </Form.Group>
                    </div>

                    <div className="col-md-6 mb-3">
                      <Form.Group>
                        <Form.Label>
                          City<span className="required-star">*</span>
                        </Form.Label>
                        <Form.Control
                          type="text"
                          value={data.mailing_address.city || ""}
                          name="city"
                          onChange={(e) =>
                            setData((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: e.target.value,
                              },
                            }))
                          }
                        ></Form.Control>
                        {dataError.mailing_address.city && (
                          <p className="error">Please enter city</p>
                        )}
                      </Form.Group>
                    </div>

                    <div className="col-md-6 mb-3">
                      <Form.Group className="form-block mb-3">
                        <Form.Label htmlFor="State">
                          State <span className="required-star">*</span>
                        </Form.Label>
                        <Form.Select
                          id="mailing_address_state"
                          aria-label="Default select example"
                          name="state"
                          value={data.mailing_address.state || ""}
                          onChange={(e) => {
                            setData((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: e.target.value,
                              },
                            }));
                            setDataError((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: false,
                              },
                            }));
                          }}
                        >
                          <option value="">Select State</option>
                          {usaStates.map((state) => (
                            <option
                              key={state.abbreviation}
                              value={state.abbreviation}
                            >
                              {state.name}
                            </option>
                          ))}
                        </Form.Select>
                        {dataError.mailing_address.state && (
                          <p className="error">Please enter state</p>
                        )}
                      </Form.Group>
                    </div>

                    <div className="col-md-6 mb-3">
                      <Form.Group>
                        <Form.Label>
                          Zip Code<span className="required-star">*</span>
                        </Form.Label>
                        <Form.Control
                          type="text"
                          value={data.mailing_address.zip_code || ""}
                          maxLength={10}
                          name="zip_code"
                          onChange={(e) => {
                            setData((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: formatPostalCodeWithHyphen(
                                  e.target.value
                                ),
                              },
                            }));
                            setDataError((prev: any) => ({
                              ...prev,
                              mailing_address: {
                                ...prev.mailing_address,
                                [e.target.name]: false,
                              },
                            }));
                          }}
                        ></Form.Control>
                        {dataError.mailing_address.zip_code && (
                          <p className="error">Please enter zip code</p>
                        )}
                      </Form.Group>
                    </div>
                  </div>
                )}
              </div>

              {/* --------------- Previous Medical Plans----------------- */}

              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Previous Medical Plans</span>
                </div>

                <div className="row">
                  <div className="col-12 col-md-5">
                    <p>
                      Did you previously offer a medical plan?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-3 ">
                          <Form.Check
                            type="radio"
                            id="med-1"
                            label="Yes"
                            name="had-medical-plan"
                            checked={data?.has_previous_medical_plan}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                has_previous_medical_plan: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-3 ">
                          <Form.Check
                            type="radio"
                            id="med-2"
                            checked={!data?.has_previous_medical_plan}
                            label="No"
                            name="had-medical-plan"
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                has_previous_medical_plan: false,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {data?.has_previous_medical_plan && (
                    <div className="col-12 col-md-7  side-border-design">
                      <div className="side-border-after"></div>
                      <div className="mb-3">
                        <p>
                          Was your previous medical plan self-funded?
                          <span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row">
                            <div className="col-3">
                              <Form.Check
                                type="radio"
                                label="Yes"
                                name="is-self-funded"
                                checked={
                                  data?.previous_medical_plan_details
                                    ?.was_self_funded
                                }
                                onChange={() =>
                                  setData((prev: any) => ({
                                    ...prev,
                                    previous_medical_plan_details: {
                                      ...prev.previous_medical_plan_details,
                                      was_self_funded: true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-3">
                              <Form.Check
                                type="radio"
                                label="No"
                                name="is-self-funded"
                                checked={
                                  !data?.previous_medical_plan_details
                                    ?.was_self_funded
                                }
                                onChange={() =>
                                  setData((prev: any) => ({
                                    ...prev,
                                    previous_medical_plan_details: {
                                      ...prev.previous_medical_plan_details,
                                      was_self_funded: false,
                                    },
                                  }))
                                }
                              />
                            </div>
                          </div>
                        </Form.Group>
                      </div>

                      {data?.previous_medical_plan_details?.was_self_funded ? (
                        <Form.Group className="mb-3">
                          <Form.Label>
                            Previous Legal Plan Name
                            <span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="legal_plan_name_assigned"
                            value={
                              data.previous_medical_plan_details
                                .legal_plan_name_assigned || ""
                            }
                            onChange={(e) => {
                              setData((prev: any) => ({
                                ...prev,
                                previous_medical_plan_details: {
                                  ...prev.previous_medical_plan_details,
                                  [e.target.name]: e.target.value,
                                },
                              }));
                              setDataError((prev: any) => ({
                                ...prev,
                                previous_medical_plan_details: {
                                  ...prev.previous_medical_plan_details,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          ></Form.Control>
                          {dataError.previous_medical_plan_details
                            .legal_plan_name_assigned && (
                            <p className="error">
                              Please enter legal plan name
                            </p>
                          )}
                        </Form.Group>
                      ) : (
                        <Form.Group className="mb-3">
                          <Form.Label>
                            Who was your previous carrier?
                            <span className="required-star">*</span>
                          </Form.Label>
                          <Form.Select
                            id="previous_carrier"
                            aria-label="Default select example"
                            name="previous_carrier"
                            value={
                              data?.previous_medical_plan_details
                                ?.previous_carrier
                            }
                            onChange={(e) => {
                              setData((prev: any) => ({
                                ...prev,
                                previous_medical_plan_details: {
                                  ...prev.previous_medical_plan_details,
                                  [e.target.name]: e.target.value,
                                },
                              }));
                              setDataError((prev: any) => ({
                                ...prev,
                                previous_medical_plan_details: {
                                  ...prev.previous_medical_plan_details,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          >
                            <option value="">Select Carrier</option>
                            {dropdowns?.carriers?.map((item: any, index: number) => {
                              return (
                                <option value={item?.carrier_id} key={index}>
                                  {item?.carrier_name}
                                </option>
                              );
                            })}
                          </Form.Select>
                          {dataError.previous_medical_plan_details
                            .previous_carrier && (
                            <p className="error">Please select carrier</p>
                          )}
                        </Form.Group>
                      )}

                      <div className="instruction-box">
                        {data?.previous_medical_plan_details
                          ?.was_self_funded ? (
                          <p>
                            *Since your previous medical plan was self-funded,
                            we must confirm the exact legal plan name of your
                            previous plan. This would be something along the
                            lines of “[Group Legal Name] Employee Benefit Health
                            Plan” and can typically be found on your Form 5500
                            filing or previous Summary Plan Description (SPD).
                            The legal plan name is not the name of your previous
                            administrator nor carrier. If you do not provide the
                            exact legal plan name of your previous self-funded
                            plan, it will be your responsibility to submit any
                            necessary forms to the IRS to change the legal plan
                            name of your self-funded plan.
                          </p>
                        ) : (
                          <p>
                            *Thank you for confirming that your previous medical
                            plan was not self-funded. Given that this plan is
                            self-funded, the legal plan name we will assign is
                            going to be “[Group Legal Name] Employee Benefit
                            Health Plan”. This is the legal plan name that will
                            appear on Plan Documents from Tres Health.
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* --------------- Client W9----------------- */}

              <div className="row mb-5">
                <div className="col-12">
                  <div className="row">
                    <div className="form-section-heading">
                      <span className="heading">Client W9</span>
                    </div>
                    <div className="file-box mb-3 col-12 col-md-6">
                      <div className="attach-box d-flex align-items-center justify-content-center">
                        Attach File
                        <i className="file-icon"></i>
                      </div>
                      No Files Attached
                      <Form.Group
                        controlId="formFile"
                        className="mb-3 col-6 main-attach-element"
                      >
                        <Form.Control
                          type="file"
                          value={data.client_w9 || ""}
                        />
                      </Form.Group>
                    </div>
                  </div>
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

<div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("0")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Continue <i className="right-arrow"></i>
                </button>
              </div>
              
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step1;
