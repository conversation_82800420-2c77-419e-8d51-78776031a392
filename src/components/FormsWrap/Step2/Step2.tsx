import axios from "axios";
import { useEffect, useState } from "react";
import { Form } from "react-bootstrap";
import { encryption, encryptionObj } from "../../../utils/Encrypt";
import { axiosInstance } from "../../../api/axios";
import endPoints from "../../../api/endpoints";
import { notify } from "../../../utils/NotifyToasts";
import { useParams } from "react-router-dom";

interface Step2Props {
  setStep: (step: string) => void;
  draftData: any;
  setLoader: (loader: boolean) => void;
}

const Step2: React.FC<Step2Props> = ({setLoader, draftData, setStep }) => {
  const {id} = useParams();

  const [data , setData] = useState(
    {
      "main_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null
      },
      "main_contact_has_portal_access": false,
      "is_contact_authorized_signer": false,
      "is_main_contact_internal": false,
      "is_billing_same_as_main": false,
      "billing_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null,
        "is_contact_authorized_signer": false,
      },
      "is_eligibility_same_as_main": false,
      "eligibility_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
      "contact_extension": null,
        "contact_email": null,
        "is_contact_authorized_signer": false,

      },
     "employer_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null,
        "is_contact_authorized_signer": false,
      },
    }
  )
  


  const [dataError , setDataError] = useState(
    {
      "main_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": "",
        "contact_extension": false,
        "contact_email": ""
      },
      "main_contact_has_portal_access": false,
      "is_contact_authorized_signer": false,
      "is_main_contact_internal": false,
      "is_billing_same_as_main": false,
      "billing_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": "",
        "contact_extension": false,
        "contact_email": "",
        "is_contact_authorized_signer": false,
      },
      "is_eligibility_same_as_main": false,
      "eligibility_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": "",
        "contact_extension": false,
        "contact_email": "",
        "is_contact_authorized_signer": false,
      },
     "employer_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": "",
        "contact_extension": false,
        "contact_email": "",
        "is_contact_authorized_signer": false,
      },
      "is_draft": false
    }
  );

  useEffect(()=>{
    if(draftData?.contact_info){
      setData(draftData?.contact_info)
    }
  }, [draftData])

  const handleNumericInputChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>  ) => {
      let value = e.target.value;
      // Use replace with regex to remove all spaces
      value = value.replace(/\s/g, "");
  
      // Ensure the value is numeric and no more than 10 digits
      if (value.length <= 10 && !isNaN(Number(value))) {
        return value; // Set the valid value
      }
    };

  const handleSubmitData = async (draft: boolean) => {
    const formData = new FormData();
                  let payload = await encryptionObj({...data, is_draft: draft});
                  formData.append(
                      "data",
                      new Blob([JSON.stringify(payload)], {
                          type: "application/json",
                      })
                  );
      const EncKey = await encryption(id || "");
      setLoader(true);
      axiosInstance
      .post(`${endPoints.intakeSubmit}?group_key=${encodeURIComponent(EncKey || "")}&intake_page_number=2`, formData)
      .then(async (response) => {
        setLoader(false);
        if (response?.data?.code === 200) {
          if(!draft){
            setStep("3");
          }
        }
        else{
          notify.errorToast(
            response?.data?.response ?? "Something went wrong!"
          );
        }
      })
      .catch((error) => {
        setLoader(false);
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  }

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    const emailRegex = /^[^\s@]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/; // Simple email regex
    let valid = true;

    if(!data?.main_contact?.contact_title){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_name){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_phone){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_phone: "Please enter contact phone",
      },}))
      valid=false;
    }
    if(data?.main_contact?.contact_phone?.length > 0 && data?.main_contact?.contact_phone?.length < 10){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_phone: "Please enter a valid contact phone",
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_email){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.main_contact?.contact_email)){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }

    // --------------

    if(!data?.is_main_contact_internal ){

    if(!data?.employer_contact?.contact_title){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_name){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_phone){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_phone: "Please enter contact phone",
      },}))
      valid=false;
    }
    if(data?.employer_contact?.contact_phone?.length > 0 && data?.employer_contact?.contact_phone?.length < 10){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_phone: "Please enter a valid contact phone",
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_email){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.employer_contact?.contact_email)){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }
    }


    // --------------

    // if(!data?.billing_contact?.contact_title){
    //   setDataError((prev)=>({...prev, billing_contact: {
    //     ...prev.billing_contact,
    //     contact_title: true,
    //   },}))
    //   valid=false;
    // }

    // if(!data?.billing_contact?.contact_name){
    //   setDataError((prev)=>({...prev, billing_contact: {
    //     ...prev.billing_contact,
    //     contact_name: true,
    //   },}))
    //   valid=false;
    // }

    // if(!data?.billing_contact?.contact_phone){
    //   setDataError((prev)=>({...prev, billing_contact: {
    //     ...prev.billing_contact,
    //     contact_phone: true,
    //   },}))
    //   valid=false;
    // }

    // if(!data?.billing_contact?.contact_email){
    //   setDataError((prev)=>({...prev, billing_contact: {
    //     ...prev.billing_contact,
    //     contact_email: "Please enter email",
    //   },}))
    //   valid=false;
    // }else if(!emailRegex.test(data?.billing_contact?.contact_email)){
    //   setDataError((prev)=>({...prev, billing_contact: {
    //     ...prev.billing_contact,
    //     contact_email: "Please enter a valid email",
    //   },}))
    //   valid=false;
    // }

    if(!data?.is_billing_same_as_main ) {
      if(!data?.billing_contact?.contact_title){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_title: true,
        },}))
        valid=false;
      }
  
      if(!data?.billing_contact?.contact_name){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_name: true,
        },}))
        valid=false;
      }
  
      if(!data?.billing_contact?.contact_phone){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_phone: "Please enter contact phone",
        },}))
        valid=false;
      }
      if(data?.billing_contact?.contact_phone?.length > 0 && data?.billing_contact?.contact_phone?.length < 10){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_phone: "Please enter a valid contact phone",
        },}))
        valid=false;
      }
  
      if(!data?.billing_contact?.contact_email){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_email: "Please enter email",
        },}))
        valid=false;
      }else if(!emailRegex.test(data?.billing_contact?.contact_email)){
        setDataError((prev)=>({...prev, billing_contact: {
          ...prev.billing_contact,
          contact_email: "Please enter a valid email",
        },}))
        valid=false;
      }
    }


   

   

    // --------------

    if(!data?.is_eligibility_same_as_main){

      if(!data?.eligibility_contact?.contact_title){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_title: true,
        },}))
        valid=false;
      }
  
      if(!data?.eligibility_contact?.contact_name){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_name: true,
        },}))
        valid=false;
      }
  
      if(!data?.eligibility_contact?.contact_phone){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_phone: "Please enter contact phone",
        },}))
        valid=false;
      }
      if(data?.eligibility_contact?.contact_phone?.length > 0 && data?.eligibility_contact?.contact_phone?.length < 10){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_phone: "Please enter a valid contact phone",
        },}))
        valid=false;
      }
  
      if(!data?.eligibility_contact?.contact_email){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_email: "Please enter email",
        },}))
        valid=false;
      }else if(!emailRegex.test(data?.eligibility_contact?.contact_email)){
        setDataError((prev)=>({...prev, eligibility_contact: {
          ...prev.eligibility_contact,
          contact_email: "Please enter a valid email",
        },}))
        valid=false;
      }
    }

   
   

    if(valid){
      handleSubmitData(false);
    }
  };



  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <button className="draft-button" onClick={()=>handleSubmitData(true)}>Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              {/* -----------Main Contact Details--------------- */}
              <div className="mb-5">
                <div className="row">
                  <div className="form-section-heading">
                    <span className="heading">Main Contact Details</span>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        id="contact_title"
                        value={data?.main_contact.contact_title || ""}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                      ></Form.Control>
                      {dataError?.main_contact?.contact_title && (
                        <p className="error">Please enter title</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        id="contact_name"
                        value={data?.main_contact?.contact_name || ""}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                      ></Form.Control>
                      {dataError?.main_contact?.contact_name && (
                        <p className="error">Please enter name</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        id="contact_phone"
                        maxLength={10}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: handleNumericInputChange(e),
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                        value={data?.main_contact?.contact_phone || ""}
                      ></Form.Control>
                      {dataError?.main_contact?.contact_phone && (
                        <p className="error">Please enter phone</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data?.main_contact?.contact_extension || ""}
                        name="contact_extension"
                        id="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));

                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: "",
                            },
                          }));
                        }}
                        value={data?.main_contact?.contact_email || ""}
                        name="contact_email"
                        id="contact_email"
                      ></Form.Control>
                      {dataError?.main_contact?.contact_email && (
                        <p className="error">
                          {dataError?.main_contact?.contact_email}
                        </p>
                      )}
                    </Form.Group>
                  </div>
                </div>

                {/* ---------------------Access to the Employer Portal----------- */}

                <div className="row mb-3">
                  <p>
                    Should the Main Contact have Access to the Employer Portal?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-2">
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="main_contact_has_portal_access_yes"
                          label="Yes"
                          name="main_contact_has_portal_access"
                          checked={data?.main_contact_has_portal_access}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              main_contact_has_portal_access: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="main_contact_has_portal_access_no"
                          label="No"
                          name="main_contact_has_portal_access"
                          checked={!data?.main_contact_has_portal_access}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              main_contact_has_portal_access: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>

                  {data?.main_contact_has_portal_access ? (
                    <div className="instruction-box">
                      <span className="required-star">*</span>Access will be
                      granted once the group implementation has been completed.
                      This contact will have the ability to add additional staff
                      members that they would like to have access to the
                      Employer Portal.
                    </div>
                  ) : (
                    <div className="instruction-box">
                      <span className="required-star">*</span>Please note that
                      saying “No” means that the group will not have access to
                      the Employer Portal. The Main Contact must be set up on
                      the Employer Portal in order to grant additional staff
                      members access to the Employer Portal.{" "}
                    </div>
                  )}
                </div>

                {/* -------------Authorized Signer of the Group----------- */}

                <div className="row mb-3">
                  <p>
                    Is the Main Contact an Authorized Signer of the Group?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="is_contact_authorized_signer_yes"
                          label="Yes"
                          name="is_contact_authorized_signer"
                          checked={data?.is_contact_authorized_signer}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_contact_authorized_signer: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="is_contact_authorized_signer_no"
                          label="No"
                          name="is_contact_authorized_signer"
                          checked={!data?.is_contact_authorized_signer}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_contact_authorized_signer: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>
                </div>

                {/* ---------------internal or external group contact?*----------------------------------- */}

                <div className="row">
                  <div className="internal">
                    <p>
                      Is the Main Contact above an internal or external group
                      contact?<span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row mb-3">
                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="is_main_contact_internal_yes"
                            label="Internal"
                            name="is_main_contact_internal"
                            checked={data?.is_main_contact_internal}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_main_contact_internal: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="is_main_contact_internal_no"
                            label="External"
                            name="is_main_contact_internal"
                            checked={!data?.is_main_contact_internal}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_main_contact_internal: false,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------------external---------------- */}

                  {!data?.is_main_contact_internal && (
                    <div className="external">
                      <div className="external-contact-info">
                        <p className="fw-medium border-bottom pb-3">
                          Since the Main Contact above is external, you must
                          provide an internal Employer Contact who is an
                          Authorized Signer.
                        </p>
                      </div>

                      <div>
                        <div className="form-section-heading form-heading-after-remove">
                          <span className="heading">
                            Employer Contact Details
                          </span>
                        </div>
                        <div className="row">
                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Title<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_title"
                                id="employer_contact_title"
                                value={
                                  data?.employer_contact?.contact_title || ""
                                }
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                              ></Form.Control>
                              {dataError?.employer_contact?.contact_title && (
                                <p className="error">Please enter title</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Name<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_name"
                                id="employer_contact_name"
                                value={
                                  data?.employer_contact?.contact_name || ""
                                }
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                              ></Form.Control>
                              {dataError?.employer_contact?.contact_name && (
                                <p className="error">Please enter name</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Phone<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_phone"
                                id="employer_contact_phone"
                                maxLength={10}
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]:
                                        handleNumericInputChange(e),
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                                value={
                                  data?.employer_contact?.contact_phone || ""
                                }
                              ></Form.Control>
                              {dataError?.employer_contact?.contact_phone && (
                                <p className="error">Please enter phone</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Extension{" "}
                                <span className="optional-text">
                                  (Optional)
                                </span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }))
                                }
                                value={
                                  data?.employer_contact?.contact_extension ||
                                  ""
                                }
                                name="contact_extension"
                                id="employer_contact_extension"
                              ></Form.Control>
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Email Address
                                <span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: "",
                                    },
                                  }));
                                }}
                                value={
                                  data?.employer_contact?.contact_email || ""
                                }
                                name="contact_email"
                                id="employer_contact_email"
                              ></Form.Control>
                              {dataError?.employer_contact?.contact_email && (
                                <p className="error">
                                  {dataError?.employer_contact?.contact_email}
                                </p>
                              )}
                            </Form.Group>
                          </div>
                        </div>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?
                          <span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row mb-3">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                label="Yes"
                                name="employer_contact_signer"
                                id="employer_contact_signer_no"
                                checked={
                                  data?.employer_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      is_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                label="No"
                                name="employer_contact_signer"
                                id="employer_contact_signer_yes"
                                checked={
                                  !data?.employer_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      is_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError?.employer_contact?.is_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* -------------Billing Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading col-12">
                  <span className="heading">Billing Contact Details</span>
                </div>

                <div className="col-12">
                  <p>
                    Is the Billing Contact same as the Main Contact?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row mb-3">
                      <div className="col-md-1 col-3">
                        <Form.Check
                          type="radio"
                          label="Yes"
                          name="is_billing_same_as_main"
                          id="is_billing_same_as_main_no"
                          checked={data?.is_billing_same_as_main}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_billing_same_as_main: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-md-1 col-3">
                        <Form.Check
                          type="radio"
                          label="No"
                          name="is_billing_same_as_main"
                          id="is_billing_same_as_main_yes"
                          checked={!data?.is_billing_same_as_main}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_billing_same_as_main: false,
                            }))
                          }
                        />
                      </div>
                      {dataError?.is_billing_same_as_main && (
                        <p className="error">Please select an option</p>
                      )}
                    </div>
                  </Form.Group>

                  {!data?.is_billing_same_as_main && (
                    <div className="row">
                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Title<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="contact_title"
                            id="billing_contact_title"
                            value={data?.billing_contact?.contact_title || ""}
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          ></Form.Control>
                          {dataError?.billing_contact?.contact_title && (
                            <p className="error">Please enter title</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Name<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="contact_name"
                            id="billing_contact_name"
                            value={data?.billing_contact?.contact_name || ""}
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          ></Form.Control>
                          {dataError?.billing_contact?.contact_name && (
                            <p className="error">Please enter name</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Phone<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            maxLength={10}
                            name="contact_phone"
                            id="billing_contact_name"
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: handleNumericInputChange(e),
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                            value={data?.billing_contact?.contact_phone || ""}
                          ></Form.Control>
                          {dataError?.billing_contact?.contact_phone && (
                            <p className="error">Please enter phone</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Extension{" "}
                            <span className="optional-text">(Optional)</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            onChange={(e) =>
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }))
                            }
                            value={data?.billing_contact?.contact_extension || ""}
                            name="contact_extension"
                            id="billing_contact_extension"
                          ></Form.Control>
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Email Address
                            <span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: "",
                                },
                              }));
                            }}
                            value={data?.billing_contact?.contact_email || ""}
                            name="contact_email"
                            id="billing_contact_email"
                          ></Form.Control>
                          {dataError?.billing_contact?.contact_email && (
                            <p className="error">
                              {dataError?.billing_contact?.contact_email}
                            </p>
                          )}
                        </Form.Group>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?
                          <span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row mb-3">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="billing_contact_signer_yes"
                                label="Yes"
                                name="billing_contact_signer"
                                checked={
                                  data?.billing_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    billing_contact: {
                                      ...prev.billing_contact,
                                      is_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="billing_contact_signer_no"
                                label="No"
                                name="billing_contact_signer"
                                checked={
                                  !data?.billing_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    billing_contact: {
                                      ...prev.billing_contact,
                                      is_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError?.billing_contact?.is_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}

                  {/* ---------------------------------------------------------- */}
                </div>
              </div>

              {/* -------------Eligibility Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading col-12">
                  <span className="heading">Eligibility Contact Details</span>
                </div>

                <div className="col-12">
                  <div className="mb-3">
                    <p>
                      Is the Eligibility Contact same as the Main Contact?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="is_eligibility_same_as_main-yes"
                            label="Yes"
                            name="is_eligibility_same_as_main"
                            checked={data?.is_eligibility_same_as_main}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_eligibility_same_as_main: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="is_eligibility_same_as_main-no"
                            label="No"
                            name="is_eligibility_same_as_main"
                            checked={!data?.is_eligibility_same_as_main}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_eligibility_same_as_main: false,
                              }))
                            }
                          />
                        </div>
                        {dataError?.is_eligibility_same_as_main && (
                          <p className="error">Please select an option</p>
                        )}
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------no-case------------------ */}
                  {!data?.is_eligibility_same_as_main && (
                    <div>
                      <div className="row">
                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Title<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="contact_title"
                              id="eligibility_contact_title"
                              value={
                                data?.eligibility_contact?.contact_title || ""
                              }
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                            ></Form.Control>
                            {dataError?.eligibility_contact?.contact_title && (
                              <p className="error">Please enter title</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Name<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="contact_name"
                              id="eligibility_contact_name"
                              value={
                                data?.eligibility_contact?.contact_name || ""
                              }
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                            ></Form.Control>
                            {dataError?.eligibility_contact?.contact_name && (
                              <p className="error">Please enter name</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Phone<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              maxLength={10}
                              name="contact_phone"
                              id="eligibility_contact_phone"
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]:
                                      handleNumericInputChange(e),
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                              value={
                                data?.eligibility_contact?.contact_phone || ""
                              }
                            ></Form.Control>
                            {dataError?.eligibility_contact?.contact_phone && (
                              <p className="error">Please enter phone</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Extension{" "}
                              <span className="optional-text">(Optional)</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              onChange={(e) =>
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }))
                              }
                              value={
                                data?.eligibility_contact?.contact_extension || ""
                              }
                              name="contact_extension"
                              id="eligibility_contact_extension"
                            ></Form.Control>
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Email Address
                              <span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: "",
                                  },
                                }));
                              }}
                              value={
                                data?.eligibility_contact?.contact_email || ""
                              }
                              name="contact_email"
                              id="eligibility_contact_email"
                            ></Form.Control>
                            {dataError?.eligibility_contact?.contact_email && (
                              <p className="error">
                                {dataError?.eligibility_contact?.contact_email}
                              </p>
                            )}
                          </Form.Group>
                        </div>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?<span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                label="Yes"
                                name="eligibility_contact_signer"
                                id="eligibility_contact_signer_yes"
                                checked={
                                  !data?.eligibility_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    eligibility_contact: {
                                      ...prev.eligibility_contact,
                                      is_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                label="No"
                                name="eligibility_contact_signer"
                                id="eligibility_contact_signer_no"
                                checked={
                                  !data?.eligibility_contact
                                    ?.is_contact_authorized_signer
                                }
                                onChange={() =>
                                  setData((prev) => ({
                                    ...prev,
                                    eligibility_contact: {
                                      ...prev.eligibility_contact,
                                      is_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError?.eligibility_contact?.is_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("1")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Continue <i className="right-arrow"></i>
                </button>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step2;
