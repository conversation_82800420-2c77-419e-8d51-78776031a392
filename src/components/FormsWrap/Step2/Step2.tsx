import { axiosInstance } from "@/api/axios";
import endPoints from "@/api/endpoints";
import { encryption, encryptionObj } from "@/utils/Encrypt";
import { notify } from "@/utils/NotifyToasts";
import axios from "axios";
import { useEffect, useState } from "react";
import { Form } from "react-bootstrap";

interface Step2Props {
  setStep: (step: string) => void;
}

const Step2: React.FC<Step2Props> = ({ draftData, setStep }) => {

  const [data , setData] = useState(
    {
      "main_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null
      },
      "main_contact_has_portal_access": false,
      "is_main_contact_authorized_signer": false,
      "is_main_contact_internal": false,
      "is_billing_same_as_main": false,
      "billing_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null,
        "is_eligibility_contact_authorized_signer": false,
      },
      "is_eligibility_same_as_main": false,
      "eligibility_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
      "contact_extension": null,
        "contact_email": null,
        "is_eligibility_contact_authorized_signer": false,

      },
     "employer_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null,
        "is_eligibility_contact_authorized_signer": false,
      },
    }
  )


  const [dataError , setDataError] = useState(
    {
      "main_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": ""
      },
      "main_contact_has_portal_access": false,
      "is_main_contact_authorized_signer": false,
      "is_main_contact_internal": false,
      "is_billing_same_as_main": false,
      "billing_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": "",
        "is_eligibility_contact_authorized_signer": false,
      },
      "is_eligibility_same_as_main": false,
      "eligibility_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": "",
        "is_eligibility_contact_authorized_signer": false,
      },
     "employer_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": "",
        "is_eligibility_contact_authorized_signer": false,
      },
      "is_draft": false
    }
  );

  useEffect(()=>{
    if(draftData?.contact_info){
      // setData(draftData?.contact_info)
    }
  }, [draftData])

  const handleNumericInputChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>  ) => {
      let value = e.target.value;
      // Use replace with regex to remove all spaces
      value = value.replace(/\s/g, "");
  
      // Ensure the value is numeric and no more than 10 digits
      if (value.length <= 10 && !isNaN(Number(value))) {
        return value; // Set the valid value
      }
    };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    const emailRegex = /^[^\s@]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/; // Simple email regex
    let valid = true;

    if(!data?.main_contact?.contact_title){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_name){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_phone){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_phone: true,
      },}))
      valid=false;
    }

    if(!data?.main_contact?.contact_email){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.main_contact?.contact_email)){
      setDataError((prev)=>({...prev, main_contact: {
        ...prev.main_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }

    // --------------

    if(!data?.employer_contact?.contact_title){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_name){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_phone){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_phone: true,
      },}))
      valid=false;
    }

    if(!data?.employer_contact?.contact_email){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.employer_contact?.contact_email)){
      setDataError((prev)=>({...prev, employer_contact: {
        ...prev.employer_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }

    // --------------

    if(!data?.billing_contact?.contact_title){
      setDataError((prev)=>({...prev, billing_contact: {
        ...prev.billing_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.billing_contact?.contact_name){
      setDataError((prev)=>({...prev, billing_contact: {
        ...prev.billing_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.billing_contact?.contact_phone){
      setDataError((prev)=>({...prev, billing_contact: {
        ...prev.billing_contact,
        contact_phone: true,
      },}))
      valid=false;
    }

    if(!data?.billing_contact?.contact_email){
      setDataError((prev)=>({...prev, billing_contact: {
        ...prev.billing_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.billing_contact?.contact_email)){
      setDataError((prev)=>({...prev, billing_contact: {
        ...prev.billing_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }

    // --------------

    if(!data?.eligibility_contact?.contact_title){
      setDataError((prev)=>({...prev, eligibility_contact: {
        ...prev.eligibility_contact,
        contact_title: true,
      },}))
      valid=false;
    }

    if(!data?.eligibility_contact?.contact_name){
      setDataError((prev)=>({...prev, eligibility_contact: {
        ...prev.eligibility_contact,
        contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.eligibility_contact?.contact_phone){
      setDataError((prev)=>({...prev, eligibility_contact: {
        ...prev.eligibility_contact,
        contact_phone: true,
      },}))
      valid=false;
    }

    if(!data?.eligibility_contact?.contact_email){
      setDataError((prev)=>({...prev, eligibility_contact: {
        ...prev.eligibility_contact,
        contact_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.eligibility_contact?.contact_email)){
      setDataError((prev)=>({...prev, eligibility_contact: {
        ...prev.eligibility_contact,
        contact_email: "Please enter a valid email",
      },}))
      valid=false;
    }
   
   

    if(valid){
      const convertToFormData = (data: any) => {
        const formData = new FormData();
      
        const appendFormData = (formData: FormData, data: any, parentKey: string | null = null) => {
          Object.entries(data).forEach(([key, value]) => {
            const formKey = parentKey ? `${parentKey}[${key}]` : key;
      
            if (value instanceof File) {
              formData.append(formKey, value);
            } else if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
              appendFormData(formData, value, formKey);
            } else {
              formData.append(formKey, value ?? '');
            }
          });
        };
      
        appendFormData(formData, data);
        return formData;
      };
      
      const formDataPayload = await encryptionObj(data);
      const EncKey = await encryption("12345");
      axiosInstance
      .post(`${endPoints.intakeSubmit}?group_key=${encodeURIComponent(EncKey)}&intake_page_number=2`, {data: formDataPayload})
      .then(async (response) => {
        if (response?.data?.code === 200) {
          setStep("3");
        }
      })
      .catch((error) => {
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
    }
  };



  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <button className="draft-button">Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              {/* -----------Main Contact Details--------------- */}
              <div className="mb-5">
                <div className="row">
                  <div className="form-section-heading">
                    <span className="heading">Main Contact Details</span>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        id="contact_title"
                        value={data.main_contact.contact_title || ""}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                      ></Form.Control>
                      {dataError.main_contact.contact_title && (
                        <p className="error">Please enter title</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        id="contact_name"
                        value={data.main_contact.contact_name || ""}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                      ></Form.Control>
                      {dataError.main_contact.contact_name && (
                        <p className="error">Please enter name</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        maxLength={10}
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: handleNumericInputChange(e),
                            },
                          }));
                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: false,
                            },
                          }));
                        }}
                        value={data.main_contact.contact_phone || ""}
                      ></Form.Control>
                      {dataError.main_contact.contact_phone && (
                        <p className="error">Please enter phone</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.main_contact.contact_extension || ""}
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) => {
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }));

                          setDataError((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: "",
                            },
                          }));
                        }}
                        value={data?.main_contact?.contact_email || ""}
                        name="contact_email"
                      ></Form.Control>
                      {dataError.main_contact.contact_email && (
                        <p className="error">
                          {dataError.main_contact.contact_email}
                        </p>
                      )}
                    </Form.Group>
                  </div>
                </div>

                {/* ---------------------Access to the Employer Portal----------- */}

                <div className="row mb-3">
                  <p>
                    Should the Main Contact have Access to the Employer Portal?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-2">
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="has-access"
                          label="Yes"
                          name="main_contact_has_portal_access"
                          checked={data?.main_contact_has_portal_access}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              main_contact_has_portal_access: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="no-access"
                          label="No"
                          name="main_contact_has_portal_access"
                          checked={!data?.main_contact_has_portal_access}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              main_contact_has_portal_access: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>

                  {data?.main_contact_has_portal_access ? (
                    <div className="instruction-box">
                      <span className="required-star">*</span>Access will be
                      granted once the group implementation has been completed.
                      This contact will have the ability to add additional staff
                      members that they would like to have access to the
                      Employer Portal.
                    </div>
                  ) : (
                    <div className="instruction-box">
                      <span className="required-star">*</span>Please note that
                      saying “No” means that the group will not have access to
                      the Employer Portal. The Main Contact must be set up on
                      the Employer Portal in order to grant additional staff
                      members access to the Employer Portal.{" "}
                    </div>
                  )}
                </div>

                {/* -------------Authorized Signer of the Group----------- */}

                <div className="row mb-3">
                  <p>
                    Is the Main Contact an Authorized Signer of the Group?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="authorized"
                          label="Yes"
                          name="is_main_contact_authorized_signer"
                          checked={data?.is_main_contact_authorized_signer}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_main_contact_authorized_signer: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="unauthorized"
                          label="No"
                          name="is_main_contact_authorized_signer"
                          checked={!data?.is_main_contact_authorized_signer}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_main_contact_authorized_signer: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>
                </div>

                {/* ---------------internal or external group contact?*----------------------------------- */}

                <div className="row">
                  <div className="internal">
                    <p>
                      Is the Main Contact above an internal or external group
                      contact?<span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="internal"
                            label="Internal"
                            name="is_main_contact_internal"
                            checked={data?.is_main_contact_internal}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_main_contact_internal: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="external"
                            label="External"
                            name="is_main_contact_internal"
                            checked={!data?.is_main_contact_internal}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_main_contact_internal: false,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------------external---------------- */}

                  {!data?.is_main_contact_internal && (
                    <div className="external">
                      <div className="external-contact-info">
                        <p>
                          Since the Main Contact above is external, you must
                          provide an internal Employer Contact who is an
                          Authorized Signer.
                        </p>
                      </div>

                      <div>
                        <div className="form-section-heading">
                          <span className="heading">
                            Employer Contact Details
                          </span>
                        </div>
                        <div className="row">
                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Title<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_title"
                                value={
                                  data?.employer_contact?.contact_title || ""
                                }
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                              ></Form.Control>
                              {dataError.employer_contact.contact_title && (
                                <p className="error">Please enter title</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Name<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_name"
                                value={
                                  data?.employer_contact?.contact_name || ""
                                }
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                              ></Form.Control>
                              {dataError.employer_contact.contact_name && (
                                <p className="error">Please enter name</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Phone<span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                name="contact_phone"
                                maxLength={10}
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]:
                                        handleNumericInputChange(e),
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: false,
                                    },
                                  }));
                                }}
                                value={
                                  data?.employer_contact?.contact_phone || ""
                                }
                              ></Form.Control>
                              {dataError.employer_contact.contact_phone && (
                                <p className="error">Please enter phone</p>
                              )}
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Extension{" "}
                                <span className="optional-text">
                                  (Optional)
                                </span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }))
                                }
                                value={
                                  data?.employer_contact?.contact_extension ||
                                  ""
                                }
                                name="contact_extension"
                              ></Form.Control>
                            </Form.Group>
                          </div>

                          <div className="col-12 col-md-6 mb-3">
                            <Form.Group>
                              <Form.Label>
                                Email Address
                                <span className="required-star">*</span>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                onChange={(e) => {
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: e.target.value,
                                    },
                                  }));

                                  setDataError((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      [e.target.name]: "",
                                    },
                                  }));
                                }}
                                value={
                                  data?.employer_contact?.contact_email || ""
                                }
                                name="contact_email"
                              ></Form.Control>
                              {dataError.employer_contact.contact_email && (
                                <p className="error">
                                  {dataError.employer_contact.contact_email}
                                </p>
                              )}
                            </Form.Group>
                          </div>
                        </div>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?
                          <span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row mb-3">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="has-access"
                                label="Yes"
                                name="employer_contact_signer"
                                checked={
                                  data?.employer_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      is_eligibility_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="no-access"
                                label="No"
                                name="employer_contact_signer"
                                checked={
                                  !data?.employer_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    employer_contact: {
                                      ...prev.employer_contact,
                                      is_eligibility_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError?.employer_contact?.is_eligibility_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* -------------Billing Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading col-12">
                  <span className="heading">Billing Contact Details</span>
                </div>

                <div className="col-12">
                  <p>
                    Is the Billing Contact same as the Main Contact?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row mb-3">
                      <div className="col-md-1 col-3">
                        <Form.Check
                          type="radio"
                          id="has-access"
                          label="Yes"
                          name="is_billing_same_as_main"
                          checked={data?.is_billing_same_as_main}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_billing_same_as_main: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-md-1 col-3">
                        <Form.Check
                          type="radio"
                          id="no-access"
                          label="No"
                          name="is_billing_same_as_main"
                          checked={!data?.is_billing_same_as_main}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_billing_same_as_main: false,
                            }))
                          }
                        />
                      </div>
                      {dataError.is_billing_same_as_main && (
                        <p className="error">Please select an option</p>
                      )}
                    </div>
                  </Form.Group>

                  {!data?.is_billing_same_as_main && (
                    <div className="row">
                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Title<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="contact_title"
                            value={data.billing_contact.contact_title || ""}
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          ></Form.Control>
                          {dataError.billing_contact.contact_title && (
                            <p className="error">Please enter title</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Name<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="contact_name"
                            value={data.billing_contact.contact_name || ""}
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                          ></Form.Control>
                          {dataError.billing_contact.contact_name && (
                            <p className="error">Please enter name</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Phone<span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            maxLength={10}
                            name="contact_phone"
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: handleNumericInputChange(e),
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: false,
                                },
                              }));
                            }}
                            value={data.billing_contact.contact_phone || ""}
                          ></Form.Control>
                          {dataError.billing_contact.contact_phone && (
                            <p className="error">Please enter phone</p>
                          )}
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Extension{" "}
                            <span className="optional-text">(Optional)</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            onChange={(e) =>
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }))
                            }
                            value={data.billing_contact.contact_extension || ""}
                            name="contact_extension"
                          ></Form.Control>
                        </Form.Group>
                      </div>

                      <div className="col-12 col-md-6 mb-3">
                        <Form.Group>
                          <Form.Label>
                            Email Address
                            <span className="required-star">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            onChange={(e) => {
                              setData((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: e.target.value,
                                },
                              }));

                              setDataError((prev) => ({
                                ...prev,
                                billing_contact: {
                                  ...prev.billing_contact,
                                  [e.target.name]: "",
                                },
                              }));
                            }}
                            value={data.billing_contact.contact_email || ""}
                            name="contact_email"
                          ></Form.Control>
                          {dataError.billing_contact.contact_email && (
                            <p className="error">
                              {dataError.billing_contact.contact_email}
                            </p>
                          )}
                        </Form.Group>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?
                          <span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row mb-3">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="has-access"
                                label="Yes"
                                name="billing_contact_signer"
                                checked={
                                  data?.billing_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    billing_contact: {
                                      ...prev.billing_contact,
                                      is_eligibility_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="no-access"
                                label="No"
                                name="billing_contact_signer"
                                checked={
                                  !data?.billing_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    billing_contact: {
                                      ...prev.billing_contact,
                                      is_eligibility_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError.billing_contact
                              .is_eligibility_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}

                  {/* ---------------------------------------------------------- */}
                </div>
              </div>

              {/* -------------Eligibility Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading col-12">
                  <span className="heading">Eligibility Contact Details</span>
                </div>

                <div className="col-12">
                  <div className="mb-3">
                    <p>
                      Is the Eligibility Contact same as the Main Contact?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="eligibility-yes"
                            label="Yes"
                            name="is_eligibility_same_as_main"
                            checked={data?.is_eligibility_same_as_main}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_eligibility_same_as_main: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-md-1 col-3">
                          <Form.Check
                            type="radio"
                            id="eligibility-no"
                            label="No"
                            name="is_eligibility_same_as_main"
                            checked={!data?.is_eligibility_same_as_main}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_eligibility_same_as_main: false,
                              }))
                            }
                          />
                        </div>
                        {dataError.is_eligibility_same_as_main && (
                          <p className="error">Please select an option</p>
                        )}
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------no-case------------------ */}
                  {!data?.is_eligibility_same_as_main && (
                    <div>
                      <div className="row">
                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Title<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="contact_title"
                              value={
                                data.eligibility_contact.contact_title || ""
                              }
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                            ></Form.Control>
                            {dataError.eligibility_contact.contact_title && (
                              <p className="error">Please enter title</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Name<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              name="contact_name"
                              value={
                                data.eligibility_contact.contact_name || ""
                              }
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                            ></Form.Control>
                            {dataError.eligibility_contact.contact_name && (
                              <p className="error">Please enter name</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Phone<span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              maxLength={10}
                              name="contact_phone"
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]:
                                      handleNumericInputChange(e),
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: false,
                                  },
                                }));
                              }}
                              value={
                                data.eligibility_contact.contact_phone || ""
                              }
                            ></Form.Control>
                            {dataError.eligibility_contact.contact_phone && (
                              <p className="error">Please enter phone</p>
                            )}
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Extension{" "}
                              <span className="optional-text">(Optional)</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              onChange={(e) =>
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }))
                              }
                              value={
                                data.eligibility_contact.contact_extension || ""
                              }
                              name="contact_extension"
                            ></Form.Control>
                          </Form.Group>
                        </div>

                        <div className="col-12 col-md-6 mb-3">
                          <Form.Group>
                            <Form.Label>
                              Email Address
                              <span className="required-star">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              onChange={(e) => {
                                setData((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: e.target.value,
                                  },
                                }));

                                setDataError((prev) => ({
                                  ...prev,
                                  eligibility_contact: {
                                    ...prev.eligibility_contact,
                                    [e.target.name]: "",
                                  },
                                }));
                              }}
                              value={
                                data.eligibility_contact.contact_email || ""
                              }
                              name="contact_email"
                            ></Form.Control>
                            {dataError.eligibility_contact.contact_email && (
                              <p className="error">
                                {dataError.eligibility_contact.contact_email}
                              </p>
                            )}
                          </Form.Group>
                        </div>
                      </div>

                      <div>
                        <p>
                          Is the Eligibility Contact an Authorized Signer of the
                          Group?<span className="required-star">*</span>
                        </p>
                        <Form.Group>
                          <div className="row">
                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="authorized-signer"
                                label="Yes"
                                name="eligibility_contact_signer"
                                checked={
                                  !data?.eligibility_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    eligibility_contact: {
                                      ...prev.eligibility_contact,
                                      is_eligibility_contact_authorized_signer:
                                        true,
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="col-md-1 col-3">
                              <Form.Check
                                type="radio"
                                id="unauthorized-signer"
                                label="No"
                                name="eligibility_contact_signer"
                                checked={
                                  !data?.eligibility_contact
                                    ?.is_eligibility_contact_authorized_signer
                                }
                                onChange={(e) =>
                                  setData((prev) => ({
                                    ...prev,
                                    eligibility_contact: {
                                      ...prev.eligibility_contact,
                                      is_eligibility_contact_authorized_signer:
                                        false,
                                    },
                                  }))
                                }
                              />
                            </div>
                            {dataError?.eligibility_contact?.is_eligibility_contact_authorized_signer && (
                              <p className="error">Please select an option</p>
                            )}
                          </div>
                        </Form.Group>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("1")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Continue <i className="right-arrow"></i>
                </button>
              </div>

            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step2;
