import { useState } from "react";
import { Form } from "react-bootstrap";

const Step2 = ({ setStep }) => {

  const [data , setData] = useState(
    {
      "main_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null
      },
      "main_contact_has_portal_access": null,
      "is_main_contact_authorized_signer": null,
      "is_main_contact_internal": null,
      "is_billing_same_as_main": null,
      "billing_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null
      },
      "is_eligibility_same_as_main": null,
      "eligibility_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
      "contact_extension": null,
        "contact_email": null
      },
     "employer_contact": {
        "contact_name": null,
        "contact_title": null,
        "contact_phone": null,
        "contact_extension": null,
        "contact_email": null
      },
      "is_eligibility_contact_authorized_signer": null,
      "is_draft": null
    }
  )


  const [dataError , setDataError] = useState(
    {
      "main_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": false
      },
      "main_contact_has_portal_access": false,
      "is_main_contact_authorized_signer": false,
      "is_main_contact_internal": false,
      "is_billing_same_as_main": false,
      "billing_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": false
      },
      "is_eligibility_same_as_main": false,
      "eligibility_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": false
      },
     "employer_contact": {
        "contact_name": false,
        "contact_title": false,
        "contact_phone": false,
        "contact_extension": false,
        "contact_email": false
      },
      "is_eligibility_contact_authorized_signer": false,
      "is_draft": false
    }
  )

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <p>Tres Health Group Intake Form</p>
              <button className="draft-button">Save as Draft</button>
            </div>

            <Form>
              {/* -----------Main Contact Details--------------- */}
              <div className="mb-5">
                <div className="row">
                  <div className="form-section-heading">
                    <span className="heading">Main Contact Details</span>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        value={data.main_contact.contact_title || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.main_contact.contact_title && <p className="error">Please enter title</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        value={data.main_contact.contact_name || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                     {dataError.main_contact.contact_name && <p className="error">Please enter name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.main_contact.contact_phone || ''}
                      ></Form.Control>
                      {dataError.main_contact.contact_phone && <p className="error">Please enter phone</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.main_contact.contact_extension || ''}
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            main_contact: {
                              ...prev.main_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.main_contact.contact_email || ''}
                        name="contact_email"
                      ></Form.Control>
                      {dataError.main_contact.contact_email && <p className="error">Please enter email address</p>}
                    </Form.Group>
                  </div>
                </div>

                {/* ---------------------Access to the Employer Portal----------- */}

                <div className="row mb-3">
                  <p>
                    Should the Main Contact have Access to the Employer Portal?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-2">
                    <div className="row">
                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="has-access"
                          label="Yes"
                          name="main_contact_has_portal_access"
                        />
                      </div>

                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="no-access"
                          label="No"
                          name="main_contact_has_portal_access"
                        />
                      </div>
                    </div>
                  </Form.Group>
                  {/* <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                      ></Form.Control>
                      <p className="error">Please enter title</p>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                      ></Form.Control>
                      <p className="error">Please enter name</p>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                      ></Form.Control>
                      <p className="error">Please enter phone</p>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_email"
                      ></Form.Control>
                      <p className="error">Please enter email address</p>
                    </Form.Group>
                  </div> */}

                  <div className="instruction-box">
                    <span className="required-star">*</span>Access will be
                    granted once the group implementation has been completed.
                    This contact will have the ability to add additional staff
                    members that they would like to have access to the Employer
                    Portal.
                  </div>

                  <div className="instruction-box">
                    <span className="required-star">*</span>Please note that
                    saying “No” means that the group will not have access to the
                    Employer Portal. The Main Contact must be set up on the
                    Employer Portal in order to grant additional staff members
                    access to the Employer Portal.{" "}
                  </div>
                </div>

                {/* -------------Authorized Signer of the Group----------- */}

                <div className="row mb-3">
                  <p>
                    Is the Main Contact an Authorized Signer of the Group?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row">
                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="authorized"
                          label="Yes"
                          name="is_main_contact_authorized_signer"
                        />
                      </div>

                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="unauthorized"
                          label="No"
                          name="is_main_contact_authorized_signer"
                        />
                      </div>
                    </div>
                  </Form.Group>
                </div>

                {/* ---------------internal or external group contact?*----------------------------------- */}

                <div className="row">
                  <div className="internal">
                    <p>
                      Is the Main Contact above an internal or external group
                      contact?<span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="internal"
                            label="Internal"
                            name="is_main_contact_internal"
                          />
                        </div>

                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="external"
                            label="External"
                            name="is_main_contact_internal"
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------------external---------------- */}

                  <div className="external">
                    <div className="external-contact-info">
                      <p>
                        Since the Main Contact above is external, you must
                        provide an internal Employer Contact who is an
                        Authorized Signer.
                      </p>
                    </div>

                    <div>
                      <div className="form-section-heading">
                        <span className="heading">
                          Employer Contact Details
                        </span>
                      </div>
                      <div className="row">
                      <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        value={data.employer_contact.contact_title || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            employer_contact: {
                              ...prev.employer_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.employer_contact.contact_title && <p className="error">Please enter title</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        value={data.employer_contact.contact_name || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            employer_contact: {
                              ...prev.employer_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.employer_contact.contact_name  && <p className="error">Please enter name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            employer_contact: {
                              ...prev.employer_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.employer_contact.contact_phone || ''}
                      ></Form.Control>
                      {dataError.employer_contact.contact_phone && <p className="error">Please enter phone</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            employer_contact: {
                              ...prev.employer_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.employer_contact.contact_extension || ''}
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            employer_contact: {
                              ...prev.employer_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.employer_contact.contact_email || ''}
                        name="contact_email"
                      ></Form.Control>
                      {dataError.employer_contact.contact_email && <p className="error">Please enter email address</p>}
                    </Form.Group>
                  </div>
                      </div>
                    </div>

                    <div>
                      <p>
                        Is the Eligibility Contact an Authorized Signer of the
                        Group?
                        <span className="required-star">*</span>
                      </p>
                      <Form.Group>
                        <div className="row mb-3">
                          <div className="col-1">
                            <Form.Check
                              type="radio"
                              id="has-access"
                              label="Yes"
                              name="is_eligibility_contact_authorized_signer"
                            />
                          </div>

                          <div className="col-1">
                            <Form.Check
                              type="radio"
                              id="no-access"
                              label="No"
                              name="is_eligibility_contact_authorized_signer"
                            />
                          </div>
                          {dataError.is_eligibility_contact_authorized_signer && <p className="error">Please select an option</p>}
                          </div>
                        </Form.Group>
                    </div>
                  </div>
                </div>
              </div>

              {/* -------------Billing Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Billing Contact Details</span>
                </div>

                <div className="row">
                  <p>
                    Is the Billing Contact same as the Main Contact?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group>
                    <div className="row mb-3">
                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="has-access"
                          label="Yes"
                          name="is_billing_same_as_main"
                        />
                      </div>

                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="no-access"
                          label="No"
                          name="is_billing_same_as_main"
                        />
                      </div>
                      {dataError.is_billing_same_as_main && <p className="error">Please select an option</p>}
                    </div>
                  </Form.Group>

                  <div className="row">
                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        value={data.billing_contact.contact_title || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            billing_contact: {
                              ...prev.billing_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.billing_contact.contact_title && (<p className="error">Please enter title</p>)}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        value={data.billing_contact.contact_name || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            billing_contact: {
                              ...prev.billing_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.billing_contact.contact_name && <p className="error">Please enter name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            billing_contact: {
                              ...prev.billing_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.billing_contact.contact_phone || ''}
                      ></Form.Control>
                      {dataError.billing_contact.contact_phone && <p className="error">Please enter phone</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            billing_contact: {
                              ...prev.billing_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.billing_contact.contact_extension || ''}
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            billing_contact: {
                              ...prev.billing_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.billing_contact.contact_email || ''}
                        name="contact_email"
                      ></Form.Control>
                      {dataError.billing_contact.contact_email && <p className="error">Please enter email address</p>}
                    </Form.Group>
                  </div>
                  </div>

                  {/* ---------------------------------------------------------- */}

                  <div>
                    <p>
                      Is the Eligibility Contact an Authorized Signer of the
                      Group?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row mb-3">
                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="has-access"
                            label="Yes"
                            name="is_eligibility_contact_authorized_signer"
                          />
                        </div>

                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="no-access"
                            label="No"
                            name="is_eligibility_contact_authorized_signer"
                          />
                        </div>
                        {dataError.is_eligibility_contact_authorized_signer && <p className="error">Please select an option</p>}
                      </div>
                    </Form.Group>
                  </div>
                </div>
              </div>

              {/* -------------Eligibility Contact Details----------------- */}
              <div className="row mb-5">
                <div className="form-section-heading">
                  <span className="heading">Eligibility Contact Details</span>
                </div>

                <div className="row">
                  <div className="mb-3">
                    <p>
                      Is the Eligibility Contact same as the Main Contact?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="eligibility-yes"
                            label="Yes"
                            name="is_eligibility_same_as_main"
                          />
                        </div>

                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="eligibility-no"
                            label="No"
                            name="is_eligibility_same_as_main"
                          />
                        </div>
                        {dataError.is_eligibility_same_as_main && <p className="error">Please select an option</p>}
                      </div>
                    </Form.Group>
                  </div>

                  {/* ------no-case------------------ */}
                  <div>
                    <div className="row">
                    <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_title"
                        value={data.eligibility_contact.contact_title || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            eligibility_contact: {
                              ...prev.eligibility_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.eligibility_contact.contact_title && <p className="error">Please enter title</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_name"
                        value={data.eligibility_contact.contact_name || ''}
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            eligibility_contact: {
                              ...prev.eligibility_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                      ></Form.Control>
                      {dataError.eligibility_contact.contact_name && <p className="error">Please enter name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Phone<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="contact_phone"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            eligibility_contact: {
                              ...prev.eligibility_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.eligibility_contact.contact_phone || ''}
                      ></Form.Control>
                      {dataError.eligibility_contact.contact_phone && <p className="error">Please enter phone</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Extension{" "}
                        <span className="optional-text">(Optional)</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            eligibility_contact: {
                              ...prev.eligibility_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.eligibility_contact.contact_extension || ''}
                        name="contact_extension"
                      ></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group>
                      <Form.Label>
                        Email Address<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        onChange={(e) =>
                          setData((prev) => ({
                            ...prev,
                            eligibility_contact: {
                              ...prev.eligibility_contact,
                              [e.target.name]: e.target.value,
                            },
                          }))
                        }
                        value={data.eligibility_contact.contact_email || ''}
                        name="contact_email"
                      ></Form.Control>
                      {dataError.eligibility_contact.contact_email && <p className="error">Please enter email address</p>}
                    </Form.Group>
                  </div>
                    </div>

                    <div>
                      <p>
                        Is the Eligibility Contact an Authorized Signer of the
                        Group?<span className="required-star">*</span>
                      </p>
                      <Form.Group>
                        <div className="row">
                          <div className="col-1">
                            <Form.Check
                              type="radio"
                              id="authorized-signer"
                              label="Yes"
                              name="is_eligibility_contact_authorized_signer"
                            />
                          </div>

                          <div className="col-1">
                            <Form.Check
                              type="radio"
                              id="unauthorized-signer"
                              label="No"
                              name="is_eligibility_contact_authorized_signer"
                            />
                          </div>
                          {dataError.is_eligibility_contact_authorized_signer && <p className="error">Please select an option</p>}
                          </div>
                      </Form.Group>
                    </div>
                  </div>
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="row  justify-content-end">
                <div className="col-6 ">
                  <div className="row">
                    <div className="col-6">
                      <button type="button" className="transparent-btn">
                        Back
                      </button>
                    </div>

                    <div className="col-6">
                      <button
                        type="submit"
                        onClick={() => setStep("3")}
                        className="purple-btn"
                      >
                        Save & Continue
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step2;
