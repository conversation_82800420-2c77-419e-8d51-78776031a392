import { useState } from "react";
import { Form } from "react-bootstrap";
import { Link } from "react-router-dom";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface Step3Props {
  setStep: (step: string) => void;
  dropdowns: any;
}

const Step3:React.FC<Step3Props> = ({ dropdowns , setStep }) => {
  const [data , setData] = useState({
    "effective_date": null,
    "waiting_period": null,
    "selected_plans": [
        {"plan_name": null,
        "plan_code": null}
    ],
    "enrollment_file_uploaded": null,
    "is_final_enrollment_completed": false,
    "is_subject_to_cobra": null,
    "is_tres_administering_cobra": null,
    "cobra_admin_details": {
        "admin_name": null,
        "admin_address": null,
        "admin_contact_name": null,
        "admin_phone": null,
        "admin_email": null
    },
    "is_draft": null
    }
    )

    const [dataError , setDataError] = useState({
      "effective_date": false,
      "waiting_period": false,
      "selected_plans": [
          
      ],
      "enrollment_file_uploaded": false,
      "is_final_enrollment_completed": false,
      "is_subject_to_cobra": false,
      "is_tres_administering_cobra": false,
      "cobra_admin_details": {
          "admin_name": false,
          "admin_address": false,
          "admin_contact_name": false,
          "admin_phone": false,
          "admin_email": ""
      },
      "is_draft": false
      }
  );

  const formatDateToYMD = (dateStr: any) => {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = (`0${date.getMonth() + 1}`).slice(-2); // Month is 0-based, so we add 1
        const day = (`0${date.getDate()}`).slice(-2); // Add leading zero if necessary
        return `${year}-${month}-${day}`; // Return in YYYY-MM-DD format
    }
    return dateStr; // Return the original string if it's not a valid date
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    const emailRegex = /^[^\s@]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/; // Simple email regex
    let valid = true;

    if(!data?.cobra_admin_details?.admin_name){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_name: true,
      },}))
      valid=false;
    }

    if(!data?.cobra_admin_details?.admin_address){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_address: true,
      },}))
      valid=false;
    }

    if(!data?.cobra_admin_details?.admin_contact_name){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_contact_name: true,
      },}))
      valid=false;
    }

    if(!data?.cobra_admin_details?.admin_phone){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_phone: true,
      },}))
      valid=false;
    }

    if(!data?.cobra_admin_details?.admin_email){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_email: "Please enter email",
      },}))
      valid=false;
    }else if(!emailRegex.test(data?.cobra_admin_details?.admin_email)){
      setDataError((prev)=>({...prev, cobra_admin_details: {
        ...prev.cobra_admin_details,
        admin_email: "Please enter a valid email",
      },}))
      valid=false;
    }

    if(!data?.effective_date){
      setDataError((prev)=>({...prev, effective_date: true}))
      valid=false;
    }

    if(!data?.waiting_period){
      setDataError((prev)=>({...prev, waiting_period: true,}))
      valid=false;
    }


    // if(!data?.cobra_admin_details?.admin_email){
    //   setDataError((prev)=>({...prev, cobra_admin_details: {
    //     ...prev.cobra_admin_details,
    //     admin_email: true,
    //   },}))
    //   valid=false;
    // }

    

    if(valid){
      setStep("4")
    }
  };

  const [isCobraSubject, setIsCobraSubject] = useState("");

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
          <div className="form-header">
              <button className="draft-button">Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Plan Details</span>
                </div>

                <div className="row">
                  {
                    data?.selected_plans?.map((item, index)=>{
                      return <div className="col-12 col-md-6 mb-3" key={index}>
                        <Form.Group className="form-block mb-3">
                          <Form.Label htmlFor="Plan 1">Plan {index+1}</Form.Label>
                          <Form.Select
                            className="dark-form-input"
                            aria-label="Default select example"
                            name="plans"
                            disabled
                          >
                            <option value="">MEC 1</option>
                          </Form.Select>
                        </Form.Group>
                      </div>
                    })
                  }

                  {/* <div className="col-12 col-md-6 mb-3">
                    <div className="form-block">
                    <p className="form-label d-none d-md-block opacity-0">attach button</p>
                    <button type="button" className="w-100 attach-box d-flex align-items-center justify-content-center" onClick={() => {setData((prev: any) => ({
                      ...prev,
                      selected_plans: [...prev.selected_plans, {plan_name: null, plan_code: null}]
                    }))}}>
                  Add More Plans
                  <i className="file-icon"></i>
                  </button>
                    </div>
                  </div> */}

                 

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="effective-date" >
                        Effective Date<span className="required-star">*</span>
                      </Form.Label>
                       <div className="position-relative">
                                                  <DatePicker
                                                      showYearDropdown
                                                      scrollableYearDropdown
                                                      yearDropdownItemNumber={100}
                                                      selected={data.effective_date ? new Date(data.effective_date) : null}
                                                      onChange={(e) => {
                                                        setData((prev: any) => ({
                                                          ...prev,
                                                          effective_date: formatDateToYMD(e),
                                                        }))
                                                      }}
                                                      dateFormat="MM-dd-yyyy"
                                                      className="form-control"
                                                      placeholderText="mm-dd-yyyy"
                                                      autoComplete="off"
                                                      onKeyDown={(e) => e.preventDefault()}
                                                  />
                                                  <i className="calender-icon"></i>
                                              </div>
                      {dataError.effective_date && <p className="error">Please enter date</p>}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Waiting Period">
                        Waiting Period<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Select
                        id="waitingPeriod"
                        aria-label="Default select example"
                        name="waitingPeriod"
                      >
                        <option value="">Select Waiting Period</option>
                        {
                          dropdowns?.waiting_periods?.map((item: any, index: number)=> {
                            return <option value={item?.waiting_period_id} key={index}>{item?.display_name}</option>
                          })
                        }
                      </Form.Select>
                      {dataError.waiting_period && <p className="error">Please enter waiting period</p>}
                    </Form.Group>
                  </div>
                </div>

                {/* ----------------- */}

                <div className="mb-3 border-bottom pb-3">
                  <p>
                    Has final enrollment been completed?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-3">
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-yes"
                          label="Yes"
                          name="is_eligibility_same_as_main"
                          checked={data?.is_final_enrollment_completed}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_final_enrollment_completed: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-no"
                          label="No"
                          name="is_eligibility_same_as_main"
                          checked={!data?.is_final_enrollment_completed}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_final_enrollment_completed: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>

                  {data?.is_final_enrollment_completed ?   <div className="instruction-box">
                    <span className="required-star ">*</span>Great! Please use our template to upload final enrollment
                    here. Please keep in mind that we will review to ensure it
                    meets the applicable participation requirements.{" "}
                  </div> 
                  :
                  <div className="instruction-box">
                  <span className="required-star">*</span>No worries! We will follow up for the completed enrollment file which will be required for implementation. Please keep in mind that once we receive final enrollment, we will review to ensure it meets the applicable participation requirements.{" "}
                  </div>
                  }

                
                 
                </div>

                {/* ------------------------- */}
                <div className="mb-3">
                  <span className="fs-6 fw-medium mb-1">
                    Enrolment File <span className="required-star">*</span>
                  </span>
                  <div className="file-box mb-3 col-12 col-md-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    No Files Attached
                    <Form.Group
                      controlId="formFile"
                      className="mb-3 col-12 col-md-6 main-attach-element"
                    >
                      <Form.Control type="file" />
                    </Form.Group>
                  </div>

                  <button className="draft-button d-flex align-items-center justify-content-center">
                    <i className="download-icon"></i> Download the Tres Health
                    Enrollment File Template
                  </button>
                </div>
              </div>

              {/* ----------------------COBRA Details-------------------- */}

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">COBRA Details</span>
                </div>

                <p>
                  COBRA generally applies to all private-sector group health
                  plans maintained by employers that had at least 20 employees
                  on more than 50 percent of its typical business days in the
                  previous calendar year. For more information
                </p>

                <div className="mb-3">
                  <span className="fw-medium fs-6">Click Here: </span>
                  <Link to="/">
                    {" "}
                    https://www.dol.gov/sites/dolgov/files/EBSA/about-ebsa/our-activities/resource-center/faqs/cobra-continuation-health-coverage.pdf
                  </Link>
                </div>

                <div className="col-12 col-md-6 mb-3 border-bottom">
                  <Form.Group className="form-block mb-3">
                    <Form.Label htmlFor="Plan 2">
                      Are you subject to COBRA?
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Select
                      id="profile-state"
                      aria-label="Default select example"
                      name="profileState"
                      onChange={(e) => setIsCobraSubject(e.target.value)}
                    >
                      <option value="">Yes</option>
                      <option value="">No</option>
                    </Form.Select>
                  </Form.Group>
                </div>

                {isCobraSubject === "yes"  && <div className="mb-3">
                  <div className="mb-3">
                    <p>
                      Has final enrollment been completed?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-3 col-md-1">
                          <Form.Check
                            type="radio"
                            label="Yes"
                            name="is_eligibility_contact_authorized_signer"
                            id="is_eligibility_contact_authorized_signer"
                            checked={data?.is_final_enrollment_completed}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_final_enrollment_completed: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-3 col-md-1">
                          <Form.Check
                            type="radio"
                            label="No"
                            name="is_eligibility_contact_authorized_signer"
                            id="is_eligibility_contact_authorized_signer"
                            checked={!data?.is_final_enrollment_completed}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_final_enrollment_completed: false,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {data?.is_final_enrollment_completed ? 
                    <div className="col-12 col-md-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>Great! If there
                      are any active COBRA participants, you can provide us
                      those details in the enrollment file. As a reminder, Tres
                      will only administer COBRA for plans offered through Tres.
                    </p>
                  </div>
                  :
                  <div className="col-12 col-md-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>No worries, we
                      will just need your COBRA Administrators information for
                      Plan Documents.
                    </p>
                  </div>
                  }

                

                  
                </div>}

                {!data?.is_final_enrollment_completed &&<div className="row">
                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control 
                      type="text"
                      name="admin_name"
                      id="admin_name"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data.cobra_admin_details.admin_name || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_name && <p className="error">Please enter admin name</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Main Contact
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control 
                      type="text"
                      name="admin_contact_name"
                      id="admin_contact_name"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data.cobra_admin_details.admin_contact_name || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_contact_name && <p className="error">Please enter admin main contact</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control 
                      type="text"
                      id="admin_address"
                      name="admin_address"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data.cobra_admin_details.admin_address || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_address && <p className="error">Please enter admin address</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Phone Number
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control 
                      type="text"
                      name="admin_phone"
                      id="admin_phone"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data.cobra_admin_details.admin_phone || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_phone && <p className="error">Please enter admin phone number</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Email Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control 
                      type="text"
                      name="admin_email"
                      id="admin_email"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: "",
                            },
                          }));
                        }
                      }
                      value={data.cobra_admin_details.admin_email || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_email && <p className="error">{dataError.cobra_admin_details.admin_email}</p>}

                    </Form.Group>
                  </div>
                </div>}
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("2")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Continue <i className="right-arrow"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3;
