import { useEffect, useState } from "react";
import { Form } from "react-bootstrap";
import "react-datepicker/dist/react-datepicker.css";
import { encryption, encryptionObj } from "../../../utils/Encrypt";
import { axiosInstance } from "../../../api/axios";
import endPoints from "../../../api/endpoints";
import { notify } from "../../../utils/NotifyToasts";
import axios from "axios";
import DatePicker from "react-datepicker";
import { useParams } from "react-router-dom";

interface Step3Props {
  setStep: (step: string) => void;
  dropdowns: any;
  draftData: any;
  setLoader: (loader: boolean) => void;
}

const Step3:React.FC<Step3Props> = ({setLoader, dropdowns , setStep, draftData }) => {
  const [fileError, setFileError] = useState<boolean>(false); // State for file error
  const [file, setFile] = useState<File | null>(null); // State for holding file
  const {id} = useParams();
  const [data , setData] = useState({
    "effective_date": null,
    "waiting_period": null,
    "selected_plans": [
        {"plan_name": null,
        "plan_code": null}
    ],
    "enrollment_file_uploaded": null,
    "is_final_enrollment_completed": false,
    "is_subject_to_cobra": null,
    "is_tres_administering_cobra": null,
    "cobra_admin_details": {
        "admin_name": null,
        "admin_address": null,
        "admin_contact_name": null,
        "admin_phone": null,
        "admin_email": null
    },
    "is_draft": null
    }
    );

    console.log("data",data)
    console.log("file", file)
    console.log("fileError", fileError)

    const [dataError , setDataError] = useState({
      "effective_date": false,
      "waiting_period": false,
      "selected_plans": [

      ],
      "enrollment_file_uploaded": false,
      "is_final_enrollment_completed": false,
      "is_subject_to_cobra": false,
      "is_tres_administering_cobra": false,
      "cobra_admin_details": {
          "admin_name": false,
          "admin_address": false,
          "admin_contact_name": false,
          "admin_phone": false,
          "admin_email": ""
      },
      "is_draft": false
      }
  );

   useEffect(()=>{
      if(draftData?.plan_info){
        setData({...draftData?.plan_info, is_subject_to_cobra: `${draftData?.plan_info?.is_subject_to_cobra}`})
      }
    }, [draftData])

  const formatDateToYMD = (dateStr: any) => {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
        const year = date.getFullYear();
        const month = (`0${date.getMonth() + 1}`).slice(-2); // Month is 0-based, so we add 1
        const day = (`0${date.getDate()}`).slice(-2); // Add leading zero if necessary
        return `${year}-${month}-${day}`; // Return in YYYY-MM-DD format
    }
    return dateStr; // Return the original string if it's not a valid date
  };

  const handleFileChange = (e: any) => {
    if (e.target.files && e.target.files[0]) {
        setFile(e.target.files[0]);
        setFileError(false); // Clear error when a file is selected
    }
  };

  const handleSubmitData = async (draft: boolean) => {
    const EncKey = await encryption(id || "");
      const formData = new FormData();
            let payload = await encryptionObj({...data, is_draft: draft});
            if(file){
              formData.append("enrollment_file", file);
            }
            formData.append(
                "data",
                new Blob([JSON.stringify(payload)], {
                    type: "application/json",
                })
            );
            setLoader(true);
      
      axiosInstance
      .post(`${endPoints.intakeSubmit}?group_key=${encodeURIComponent(EncKey || "")}&intake_page_number=3`,formData)
      .then(async (response) => {
        setLoader(false);
        if (response?.data?.code === 200) {
          if(!draft){
            setStep("4");
          }
        }
        else{
          notify.errorToast(
            response?.data?.response ?? "Something went wrong!"
          );
        }
      })
      .catch((error) => {
        setLoader(false);
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  }

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    const emailRegex = /^[^\s@]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/; // Simple email regex
    let valid = true;

    if(data?.is_subject_to_cobra === "false") {
      if(!data?.cobra_admin_details?.admin_name){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_name: true,
        },}))
        valid=false;
      }
  
      if(!data?.cobra_admin_details?.admin_address){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_address: true,
        },}))
        valid=false;
      }
  
      if(!data?.cobra_admin_details?.admin_contact_name){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_contact_name: true,
        },}))
        valid=false;
      }
  
      if(!data?.cobra_admin_details?.admin_phone){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_phone: true,
        },}))
        valid=false;
      }
  
      if(!data?.cobra_admin_details?.admin_email){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_email: "Please enter email",
        },}))
        valid=false;
      }else if(!emailRegex.test(data?.cobra_admin_details?.admin_email)){
        setDataError((prev)=>({...prev, cobra_admin_details: {
          ...prev.cobra_admin_details,
          admin_email: "Please enter a valid email",
        },}))
        valid=false;
      }
    }

  

    if(!data?.is_subject_to_cobra){
      setDataError((prev)=>({...prev, is_subject_to_cobra: true}))
      valid=false;
    }

    if(!data?.effective_date){
      setDataError((prev)=>({...prev, effective_date: true}))
      valid=false;
    }

    if(!data?.waiting_period){
      setDataError((prev)=>({...prev, waiting_period: true,}))
      valid=false;
    }

    if(data?.is_final_enrollment_completed){
      if(!file){
        setFileError(true);
        valid=false;
      }
    }


    // if(!data?.cobra_admin_details?.admin_email){
    //   setDataError((prev)=>({...prev, cobra_admin_details: {
    //     ...prev.cobra_admin_details,
    //     admin_email: true,
    //   },}))
    //   valid=false;
    // }



    if(valid){
      handleSubmitData(false);
    }
  };


  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
          <div className="form-header">
              <button className="draft-button" onClick={()=>handleSubmitData(true)}>Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Plan Details</span>
                </div>

                <div className="row">
                  {
                    data?.selected_plans?.map((_, index)=>{
                      return <div className="col-12 col-md-6 mb-3" key={index}>
                        <Form.Group className="form-block mb-3">
                          <Form.Label htmlFor="Plan 1">Plan {index+1}</Form.Label>
                          <Form.Select
                            className="dark-form-input"
                            aria-label="Default select example"
                            name="plans"
                            disabled
                          >
                            <option value="">MEC 1</option>
                          </Form.Select>
                        </Form.Group>
                      </div>
                    })
                  }

                  {/* <div className="col-12 col-md-6 mb-3">
                    <div className="form-block">
                    <p className="form-label d-none d-md-block opacity-0">attach button</p>
                    <button type="button" className="w-100 attach-box d-flex align-items-center justify-content-center" onClick={() => {setData((prev: any) => ({
                      ...prev,
                      selected_plans: [...prev.selected_plans, {plan_name: null, plan_code: null}]
                    }))}}>
                  Add More Plans
                  <i className="file-icon"></i>
                  </button>
                    </div>
                  </div> */}



                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="effective-date" >
                        Effective Date<span className="required-star">*</span>
                      </Form.Label>
                       <div className="position-relative date-block">
                                                  <DatePicker
                                                      showYearDropdown
                                                      scrollableYearDropdown
                                                      yearDropdownItemNumber={100}
                                                      selected={data.effective_date ? new Date(data.effective_date) : null}
                                                      onChange={(e) => {
                                                        setData((prev: any) => ({
                                                          ...prev,
                                                          effective_date: formatDateToYMD(e),
                                                        }));

                                                        setDataError((prev) => ({
                                                          ...prev,
                                                          effective_date: false,
                                                        }));
                                                      }}
                                                      dateFormat="MM-dd-yyyy"
                                                      className="form-control"
                                                      placeholderText="mm-dd-yyyy"
                                                      autoComplete="off"
                                                      onKeyDown={(e) => e.preventDefault()}
                                                  />
                                                  <i className="calender-icon"></i>
                                              </div>
                      {dataError?.effective_date && <p className="error">Please enter date</p>}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Waiting Period">
                        Waiting Period<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Select
                        id="waitingPeriod"
                        aria-label="Default select example"
                        name="waiting_period"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));

                          setDataError((prev) => ({
                            ...prev,
                            waiting_period: false,
                          }));
                        }}
                      >
                        <option value="">Select Waiting Period</option>
                        {
                          dropdowns?.waiting_periods?.map((item: any, index: number)=> {
                            return <option value={item?.waiting_period_id} 
                            key={index}>{item?.display_name}</option>
                          })
                        }
                      </Form.Select>
                      {dataError?.waiting_period && <p className="error">Please enter waiting period</p>}
                    </Form.Group>
                  </div>
                </div>

                {/* ----------------- */}

                <div className="mb-3 border-bottom pb-3">
                  <p>
                    Has final enrollment been completed?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-3">
                    <div className="row">
                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-yes"
                          label="Yes"
                          name="is_eligibility_same_as_main"
                          checked={data?.is_final_enrollment_completed}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_final_enrollment_completed: true,
                            }))
                          }
                        />
                      </div>

                      <div className="col-3 col-md-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-no"
                          label="No"
                          name="is_eligibility_same_as_main"
                          checked={!data?.is_final_enrollment_completed}
                          onChange={() =>
                            setData((prev: any) => ({
                              ...prev,
                              is_final_enrollment_completed: false,
                            }))
                          }
                        />
                      </div>
                    </div>
                  </Form.Group>

                  {data?.is_final_enrollment_completed ?   <div className="instruction-box">
                    <span className="required-star ">*</span>Great! Please use our template to upload final enrollment
                    here. Please keep in mind that we will review to ensure it
                    meets the applicable participation requirements.{" "}
                  </div>
                  :
                  <div className="instruction-box">
                  <span className="required-star">*</span>No worries! We will follow up for the completed enrollment file which will be required for implementation. Please keep in mind that once we receive final enrollment, we will review to ensure it meets the applicable participation requirements.{" "}
                  </div>
                  }
                </div>

                {/* ------------------------- */}


                {data?.is_final_enrollment_completed && <div className="mb-3">
                  <span className="fs-6 fw-medium mb-1">
                    Enrolment File <span className="required-star">*</span>
                  </span>
                  <div className="file-box mb-3 col-12 col-md-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    No Files Attached
                    <Form.Group
                      className="mb-3 col-12 col-md-6 main-attach-element"
                    >
                      <Form.Control type="file" onChange={handleFileChange} accept=".pdf, .doc, .docx, .xls, .xlsx" />
                    </Form.Group>
                  </div>
                      {fileError && (
                                    <p className="error">
                                        Please upload a file
                                    </p>
                                )}

                  <button className="draft-button d-flex align-items-center justify-content-center">
                    <i className="download-icon"></i> Download the Tres Health
                    Enrollment File Template
                  </button>
                </div>}
               
              </div>

              {/* ----------------------COBRA Details-------------------- */}

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">COBRA Details</span>
                </div>

                <p>
                  COBRA generally applies to all private-sector group health
                  plans maintained by employers that had at least 20 employees
                  on more than 50 percent of its typical business days in the
                  previous calendar year. For more information
                </p>

                <div className="mb-3">
                  <span className="fw-medium fs-6">Click Here: </span>
                  <a target="_blank" href="https://www.dol.gov/sites/dolgov/files/EBSA/about-ebsa/our-activities/resource-center/faqs/cobra-continuation-health-coverage.pdf">
                    {" "}
                    https://www.dol.gov/sites/dolgov/files/EBSA/about-ebsa/our-activities/resource-center/faqs/cobra-continuation-health-coverage.pdf
                  </a>
                </div>

                <div className="col-12 col-md-6 mb-3 border-bottom">
                  <Form.Group className="form-block mb-3">
                    <Form.Label htmlFor="Plan 2">
                      Are you subject to COBRA?
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Select
                      id="subjectCobra"
                      aria-label="Default select example"
                      name="subjectCobra"
                      value={data?.is_subject_to_cobra || ""}
                      onChange={(e) => {
                        setData((prev: any) => ({
                          ...prev,
                          is_subject_to_cobra: e.target.value,
                        }));
                        
                        setDataError((prev) => ({
                          ...prev,
                          is_subject_to_cobra: false,
                        }));
                      }}
                    >
                      <option value="">Select Options</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </Form.Select>
                  </Form.Group>
                  {dataError?.is_subject_to_cobra && (
                          <p className="error">Please select an option</p>
                        )}
                </div>

                {data?.is_subject_to_cobra === "true"  && <div className="mb-3">
                  <div className="mb-3">
                    <p>
                      Has final enrollment been completed?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-3 col-md-1">
                          <Form.Check
                            type="radio"
                            label="Yes"
                            name="is_eligibility_contact_authorized_signer"
                            id="is_eligibility_contact_authorized_signer"
                            checked={data?.is_final_enrollment_completed}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_final_enrollment_completed: true,
                              }))
                            }
                          />
                        </div>

                        <div className="col-3 col-md-1">
                          <Form.Check
                            type="radio"
                            label="No"
                            name="is_eligibility_contact_authorized_signer"
                            id="is_eligibility_contact_authorized_signer"
                            checked={!data?.is_final_enrollment_completed}
                            onChange={() =>
                              setData((prev: any) => ({
                                ...prev,
                                is_final_enrollment_completed: false,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  {data?.is_final_enrollment_completed ?
                    <div className="col-12 col-md-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>Great! If there
                      are any active COBRA participants, you can provide us
                      those details in the enrollment file. As a reminder, Tres
                      will only administer COBRA for plans offered through Tres.
                    </p>
                  </div>
                  :
                  <div className="col-12 col-md-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>No worries, we
                      will just need your COBRA Administrators information for
                      Plan Documents.
                    </p>
                  </div>
                  }




                </div>}

                {!data?.is_final_enrollment_completed &&<div className="row">
                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                      type="text"
                      name="admin_name"
                      id="admin_name"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data?.cobra_admin_details?.admin_name || ''}
                      ></Form.Control>
                      {dataError?.cobra_admin_details?.admin_name && <p className="error">Please enter admin name</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Main Contact
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                      type="text"
                      name="admin_contact_name"
                      id="admin_contact_name"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data?.cobra_admin_details?.admin_contact_name || ''}
                      ></Form.Control>
                      {dataError?.cobra_admin_details?.admin_contact_name && <p className="error">Please enter admin main contact</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                      type="text"
                      id="admin_address"
                      name="admin_address"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data?.cobra_admin_details?.admin_address || ''}
                      ></Form.Control>
                      {dataError?.cobra_admin_details?.admin_address && <p className="error">Please enter admin address</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Phone Number
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                      type="text"
                      name="admin_phone"
                      id="admin_phone"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: false,
                            },
                          }));
                        }
                      }
                      value={data?.cobra_admin_details?.admin_phone || ''}
                      ></Form.Control>
                      {dataError?.cobra_admin_details?.admin_phone && <p className="error">Please enter admin phone number</p>}

                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Email Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                      type="text"
                      name="admin_email"
                      id="admin_email"
                      onChange={(e) =>
                        {
                          setData((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: e.target.value,
                            },
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            cobra_admin_details: {
                              ...prev.cobra_admin_details,
                              [e.target.name]: "",
                            },
                          }));
                        }
                      }
                      value={data?.cobra_admin_details?.admin_email || ''}
                      ></Form.Control>
                      {dataError?.cobra_admin_details?.admin_email && <p className="error">{dataError.cobra_admin_details.admin_email}</p>}

                    </Form.Group>
                  </div>
                </div>}
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("2")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Continue <i className="right-arrow"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3;
