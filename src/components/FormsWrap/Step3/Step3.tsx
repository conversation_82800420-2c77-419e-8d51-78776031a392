import { useState } from "react";
import { Form } from "react-bootstrap";
import { Link } from "react-router-dom";

const Step3 = ({ setStep }) => {

  const [data , setData] = useState({
    "effective_date": null,
    "waiting_period": null,
    "selected_plans": [
        {
         "plan_name": null,
         "plan_code": null
        },
        {
         "plan_name": null,
         "plan_code": null
        }
    ],
    "enrollment_file_uploaded": null,
    "is_final_enrollment_completed": null,
    "is_subject_to_cobra": null,
    "is_tres_administering_cobra": null,
    "cobra_admin_details": {
        "admin_name": null,
        "admin_address": null,
        "admin_contact_name": null,
        "admin_phone": null,
        "admin_email": null
    },
    "is_draft": null
    }
    )

    const [dataError , setDataError] = useState({
      "effective_date": false,
      "waiting_period": false,
      "selected_plans": [
          {
           "plan_name": false,
           "plan_code": false
          },
          {
           "plan_name": false,
           "plan_code": false
          }
      ],
      "enrollment_file_uploaded": false,
      "is_final_enrollment_completed": false,
      "is_subject_to_cobra": false,
      "is_tres_administering_cobra": false,
      "cobra_admin_details": {
          "admin_name": false,
          "admin_address": false,
          "admin_contact_name": false,
          "admin_phone": false,
          "admin_email": false
      },
      "is_draft": false
      }
      )

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <p>Tres Health Group Intake Form</p>
              <button className="draft-button">Save as Draft</button>
            </div>

            <Form>
              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Plan Details</span>
                </div>

                <div className="row">
                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Plan 1">Plan 1</Form.Label>
                      <Form.Select
                        className="dark-form-input"
                        id="profile-state"
                        aria-label="Default select example"
                        name="profileState"
                      >
                        <option value="">MEC 1</option>
                      </Form.Select>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Plan 2">Plan 2</Form.Label>
                      <Form.Select
                        className="dark-form-input"
                        id="profile-state"
                        aria-label="Default select example"
                        name="profileState"
                      >
                        <option value="">MEC 2</option>
                      </Form.Select>
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Plan 3">Plan 3</Form.Label>
                      <Form.Select
                        className="dark-form-input"
                        id="profile-state"
                        aria-label="Default select example"
                        name="profileState"
                      >
                        <option value="">MEC 3</option>
                      </Form.Select>
                    </Form.Group>
                  </div>

                  <div className="file-box  mb-3 col-6 border-0">
                    <div className="form-block w-100">
                      <div className="attach-box d-flex align-items-center justify-content-center w-100">
                        Add More Plans
                        <i className="file-icon"></i>
                      </div>
                      <Form.Group
                        controlId="formFile"
                        className="mb-3 col-6 main-attach-element"
                      >
                        <Form.Label className="opacity-0">file</Form.Label>
                        <Form.Control type="file" />
                      </Form.Group>
                    </div>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="effective-date">
                        Effective Date<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Select
                        className="dark-form-input"
                        id="effective-date"
                        aria-label="Default select example"
                        name="effective-date"
                      >
                        <option value="">Select Date</option>
                      </Form.Select>
                      {data.effective_date && <p>Please enter date</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label htmlFor="Waiting Period">
                        Waiting Period<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Select
                        id="waitingPeriod"
                        aria-label="Default select example"
                        name="waitingPeriod"
                      >
                        <option value="">Select Waiting Period</option>
                      </Form.Select>
                      {data.waiting_period && <p>Please enter waiting period</p>}
                    </Form.Group>
                  </div>
                </div>

                {/* ----------------- */}

                <div className="mb-3 border-bottom pb-3">
                  <p>
                    Has final enrollment been completed?
                    <span className="required-star">*</span>
                  </p>
                  <Form.Group className="mb-3">
                    <div className="row">
                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-yes"
                          label="Yes"
                          name="is_eligibility_same_as_main"
                        />
                      </div>

                      <div className="col-1">
                        <Form.Check
                          type="radio"
                          id="eligibility-no"
                          label="No"
                          name="is_eligibility_same_as_main"
                        />
                      </div>
                    </div>
                  </Form.Group>

                  <div className="instruction-box">
                    <span className="required-star">*</span>Great! Please use our template to upload final enrollment
                    here. Please keep in mind that we will review to ensure it
                    meets the applicable participation requirements.{" "}
                  </div>
                  <div className="instruction-box">
                  <span className="required-star">*</span>Great! Please use our template to upload final enrollment
                    here. Please keep in mind that we will review to ensure it
                    meets the applicable participation requirements.{" "}
                  </div>
                </div>

                {/* ------------------------- */}
                <div className="mb-3">
                  <span className="fs-6 fw-medium mb-1">
                    Enrolment File <span className="required-star">*</span>
                  </span>
                  <div className="file-box mb-3 col-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    No Files Attached
                    <Form.Group
                      controlId="formFile"
                      className="mb-3 col-6 main-attach-element"
                    >
                      <Form.Control type="file" />
                    </Form.Group>
                  </div>

                  <button className="draft-button d-flex align-items-center justify-content-center">
                    <i className="download-icon"></i> Download the Tres Health
                    Enrollment File Template
                  </button>
                </div>
              </div>

              {/* ----------------------COBRA Details-------------------- */}

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">COBRA Details</span>
                </div>

                <p>
                  COBRA generally applies to all private-sector group health
                  plans maintained by employers that had at least 20 employees
                  on more than 50 percent of its typical business days in the
                  previous calendar year. For more information
                </p>

                <div className="mb-3">
                  <span className="fw-medium fs-6">Click Here: </span>
                  <Link to="/">
                    {" "}
                    https://www.dol.gov/sites/dolgov/files/EBSA/about-ebsa/our-activities/resource-center/faqs/cobra-continuation-health-coverage.pdf
                  </Link>
                </div>

                <div className="col-6 mb-3 border-bottom">
                  <Form.Group className="form-block mb-3">
                    <Form.Label htmlFor="Plan 2">
                      Are you subject to COBRA?
                      <span className="required-star">*</span>
                    </Form.Label>
                    <Form.Select
                      id="profile-state"
                      aria-label="Default select example"
                      name="profileState"
                    >
                      <option value="">Yes</option>
                    </Form.Select>
                  </Form.Group>
                </div>

                <div className="mb-3">
                  <div className="mb-3">
                    <p>
                      Has final enrollment been completed?
                      <span className="required-star">*</span>
                    </p>
                    <Form.Group>
                      <div className="row">
                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="authorized-signer"
                            label="Yes"
                            name="is_eligibility_contact_authorized_signer"
                          />
                        </div>

                        <div className="col-1">
                          <Form.Check
                            type="radio"
                            id="unauthorized-signer"
                            label="No"
                            name="is_eligibility_contact_authorized_signer"
                          />
                        </div>
                      </div>
                    </Form.Group>
                  </div>

                  <div className="col-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>Great! If there
                      are any active COBRA participants, you can provide us
                      those details in the enrollment file. As a reminder, Tres
                      will only administer COBRA for plans offered through Tres.
                    </p>
                  </div>

                  <div className="col-6">
                    <p className="instruction-box">
                      <span className="required-star">*</span>No worries, we
                      will just need your COBRA Administrators information for
                      Plan Documents.
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.cobra_admin_details.admin_name || ''}></Form.Control>
                      {dataError.cobra_admin_details.admin_name && <p className="error">Please enter admin name</p>}

                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Main Contact
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.cobra_admin_details.admin_contact_name || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_contact_name && <p className="error">Please enter admin main contact</p>}

                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                       value={data.cobra_admin_details.admin_address || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_address && <p className="error">Please enter admin address</p>}

                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Phone Number
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                       value={data.cobra_admin_details.admin_phone || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_phone && <p className="error">Please enter admin phone number</p>}

                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        COBRA Admin Email Address
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.cobra_admin_details.admin_email || ''}
                      ></Form.Control>
                      {dataError.cobra_admin_details.admin_email && <p className="error">Please enter admin email address</p>}

                    </Form.Group>
                  </div>
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="row  justify-content-end">
                <div className="col-6 ">
                  <div className="row">
                    <div className="col-6">
                      <button type="button" className="transparent-btn">
                        Back
                      </button>
                    </div>

                    <div className="col-6">
                      <button type="submit" 
                      onClick={() => setStep("4")}
                      className="purple-btn">
                        Save & Continue
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3;
