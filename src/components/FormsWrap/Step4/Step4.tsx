import { useState } from "react";
import { Form } from "react-bootstrap";

const Step4 = () => {

  const [data , setData] = useState(
    {
      "signed_proposal_file_name": null,
      "group_disclosure_form_file_name": null,
      "group_bank_name": null,
      "group_legal_name": null,
      "group_bank_account_number": null,
      "group_aba_routing_number": null,
      "authorized_signer_name": null,
      "authorized_signer_title": null,
      "signature_date": null,
      "signature_text": null,
      "billing_exception_granted": null,
      "ach_authorization_confirmed": null,
      "is_draft": null
    }
  )

  const [dataError , setDataError] = useState(
    {
      "signed_proposal_file_name": false,
      "group_disclosure_form_file_name": false,
      "group_bank_name": false,
      "group_legal_name": false,
      "group_bank_account_number": false,
      "group_aba_routing_number": false,
      "authorized_signer_name": false,
      "authorized_signer_title": false,
      "signature_date": false,
      "signature_text": false,
      "billing_exception_granted": false,
      "ach_authorization_confirmed": false,
      "is_draft": false
    }
  )

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <p>Tres Health Group Intake Form</p>
              <button className="draft-button">Save as Draft</button>
            </div>

            <Form>
              <div className="mb-5">
                <div className="mb-3">
                  <div className="form-section-heading">
                    <span className="heading">Billing Details</span>
                  </div>
                  <p>
                    The Tres Health Program requires ACH withdrawals for monthly
                    premium payment. Invoices are typically generated and sent
                    around the 15th of each month for coverage the following
                    month (e.g. invoiced May 15th for June coverage) and
                    withdrawn around the 25th of each month. The first invoice
                    may be off cycle. Please ensure that funds are available by
                    the withdrawal date to avoid interruption of service.
                  </p>
                </div>

                <div className="row">
                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>Group Legal Name<span className="required-star">*</span></Form.Label>
                      <Form.Control type="text"
                      value={data.group_legal_name || ''}></Form.Control>
                      {dataError.group_legal_name && <p className="error">Please enter group legal name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group Bank Name <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.group_bank_name || ''}></Form.Control>
                      {dataError.group_bank_name && <p className="error">Please enter group bank name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group Bank Account Number (Checking){" "}
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.group_bank_account_number || ''}></Form.Control>
                      {dataError.group_bank_account_number && <p className="error">Please enter group bank account number</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group ABA Routing Number{" "}
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                       value={data.group_aba_routing_number || ''}></Form.Control>
                      {dataError.group_aba_routing_number && <p className="error">Please enter group routing number</p>}
                    </Form.Group>
                  </div>

                  <div className="col-5">
                    <p className="fs-7 fw-medium">
                      {" "}
                      <span className="required-star">*</span>
                      General, if this group requires a billing exception
                      additional information may be required.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Group ACH Authorization</span>
                </div>
                <div className="mb-3">
                  <p>
                    I, as an authorized signer of the aforementioned group,
                    hereby authorize Tres Health, Inc. (“Tres”) to initiate
                    monetary withdrawals from my account at the financial
                    institution named above for the limited purposes of payment
                    of amounts due for participation in the Tres Health Program.
                    Such withdraw amount will not exceed the amounts included on
                    the current invoice (or claims funding request, if
                    applicable).
                  </p>
                  <p>
                    Further, I agree not to hold Tres responsible for any delay
                    or loss of funds due to:
                    <span className="fw-medium">
                      (i) incorrect or incomplete information supplied by me or
                      by my financial institution or
                    </span>
                    <span className="fw-medium">
                      (ii) due to an error on the part of my financial
                      institution in depositing funds to my account.
                    </span>
                  </p>

                  <p>
                    This agreement will remain in effect until Tres receives
                    written cancellation from an authorized signer of the group
                    or its financial institution.
                  </p>
                </div>

                <div className="row">
                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.authorized_signer_name || ''}></Form.Control>
                      {dataError.authorized_signer_name && <p className="error">Please enter name</p>}
                    </Form.Group>
                  </div>

                  <div className="col-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.authorized_signer_title || ''}></Form.Control>
                      {dataError.authorized_signer_title && <p className="error">Please enter title</p>}
                    </Form.Group>
                  </div>

                  <div className="col-12 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Date<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"
                      value={data.signature_date || ''}></Form.Control>
                      {dataError.signature_date && <p className="error">Please enter date</p>}
                    </Form.Group>
                  </div>

                  <div className="col-12">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Signature<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"></Form.Control>
                    </Form.Group>
                  </div>

                  <div className="col-9">
                    <Form.Check
                      type="checkbox"
                      label="Please check here if a billing arrangement exception was previously granted by Tres Health and, therefore, the bank account information is not required at this time. Please note that if you check off this box and an exception was not granted, implementation will be delayed."
                    />
                  </div>
                </div>
              </div>

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Attachments</span>
                </div>

                <div>
                  <span className="fs-6 fw-medium mb-1">
                    Signed Proposal or Renewal{" "}
                    <span className="required-star">*</span>
                  </span>
                  <div className="file-box mb-3 col-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    No Files Attached
                    <Form.Group
                      controlId="formFile"
                      className="mb-3 col-6 main-attach-element"
                    >
                      <Form.Control type="file" />
                    </Form.Group>
                  </div>
                  {dataError.signed_proposal_file_name && <p className="error">Please upload the signed proposal or renewal </p>}
                </div>

                <div>
                  <span className="fs-6 fw-medium mb-1">
                    Group Disclosure Form
                  </span>
                  <div className="file-box mb-3 col-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    No Files Attached
                    <Form.Group
                      controlId="formFile"
                      className="mb-3 col-6 main-attach-element"
                    >
                      <Form.Control type="file" />
                    </Form.Group>
                  </div>
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="row  justify-content-end">
                <div className="col-6 ">
                  <div className="row">
                    <div className="col-6">
                      <button type="button" className="transparent-btn">
                        Back
                      </button>
                    </div>

                    <div className="col-6">
                      <button type="submit" className="purple-btn">
                        Save & Complete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step4;
