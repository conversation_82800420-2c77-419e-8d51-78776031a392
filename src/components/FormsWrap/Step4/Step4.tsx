import { useEffect, useState } from "react";
import { Form } from "react-bootstrap";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Modal } from "react-bootstrap";
import SignatureFile from "../../SignatureFile/SignatureFile";
import { encryption, encryptionObj } from "../../../utils/Encrypt";
import { axiosInstance } from "../../../api/axios";
import endPoints from "../../../api/endpoints";
import { notify } from "../../../utils/NotifyToasts";
import axios from "axios";
import { useParams } from "react-router-dom";

interface Step4Props {
  setStep: (step: string) => void;
  draftData: any;
  setLoader: (loader: boolean) => void;
}



const Step4: React.FC<Step4Props> = ({setLoader, setStep, draftData }) => {
  const [fileError, setFileError] = useState<boolean>(false); // State for file error
  const {id} = useParams();
  console.log("fileError",fileError)
  const [file, setFile] = useState<Blob | null>(null); // State for holding file

  const [fileError2, setFileError2] = useState<boolean>(false); // State for file error
  const [file2, setFile2] = useState<File | null>(null); // State for holding file

  

  const [data, setData] = useState({
    signed_proposal_file_name: null,
    group_disclosure_form_file_name: null,
    group_bank_name: null,
    group_legal_name: null,
    group_bank_account_number: null,
    group_aba_routing_number: null,
    authorized_signer_name: null,
    authorized_signer_title: null,
    signature_date: null,
    signature_text: null,
    billing_exception_granted: null,
    ach_authorization_confirmed: null,
    is_draft: null,
  });

  const [dataError, setDataError] = useState({
    signed_proposal_file_name: false,
    group_disclosure_form_file_name: false,
    group_bank_name: false,
    group_legal_name: false,
    group_bank_account_number: false,
    group_aba_routing_number: false,
    authorized_signer_name: false,
    authorized_signer_title: false,
    signature_date: false,
    signature_text: false,
    billing_exception_granted: false,
    ach_authorization_confirmed: false,
    is_signature_checked: false,
  });

    useEffect(()=>{
        if(draftData?.banking_and_authorization_info){
          setData(draftData?.banking_and_authorization_info)
        }
      }, [draftData])

  const [signatureCheck, setSignatuteCheck] = useState(false);

  const formatDateToYMD = (dateStr: any) => {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = `0${date.getMonth() + 1}`.slice(-2); // Month is 0-based, so we add 1
      const day = `0${date.getDate()}`.slice(-2); // Add leading zero if necessary
      return `${year}-${month}-${day}`; // Return in YYYY-MM-DD format
    }
    return dateStr; // Return the original string if it's not a valid date
  };

  const handleFileChange2 = (e: any) => {
    if (e.target.files && e.target.files[0]) {
        setFile2(e.target.files[0]);
        setFileError2(false); // Clear error when a file is selected
    }
  };

  const handleSubmitData = async (draft: boolean) => {
    const EncKey = await encryption(id || "");
    setLoader(true);
          const formData = new FormData();
                let payload = await encryptionObj({...data, is_draft: draft});
                if(file){
                  formData.append("signed_proposal_file", file);
                }
                if(file2){
                  formData.append("digital_signature", file2);
                }
                formData.append(
                    "data",
                    new Blob([JSON.stringify(payload)], {
                        type: "application/json",
                    })
                );
          
          axiosInstance
          .post(`${endPoints.intakeSubmit}?group_key=${encodeURIComponent(EncKey || "")}&intake_page_number=4`,formData)
          .then(async (response) => {
            setLoader(false);
            if (response?.data?.code === 200) {
              if(!draft){
                setStep("completed");
              }
            }
            else{
              notify.errorToast(
                response?.data?.response ?? "Something went wrong!"
              );
            }
          })
          .catch((error) => {
            setLoader(false);
            if (axios.isAxiosError(error)) {
              notify.errorToast(
                error.response?.data?.response ?? "Something went wrong!"
              );
            }
          });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    // const emailRegex = /^[^\s@]+@[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
    let valid = true;

    if (!data?.group_legal_name) {
      setDataError((prev) => ({ ...prev, group_legal_name: true }));
      valid = false;
    }

    if (!data?.group_bank_name) {
      setDataError((prev) => ({ ...prev, group_bank_name: true }));
      valid = false;
    }

    if (!data?.group_bank_account_number) {
      setDataError((prev) => ({ ...prev, group_bank_account_number: true }));
      valid = false;
    }

    if (!data?.group_aba_routing_number) {
      setDataError((prev) => ({ ...prev, group_aba_routing_number: true }));
      valid = false;
    }

    if (!data?.authorized_signer_name) {
      setDataError((prev) => ({ ...prev, authorized_signer_name: true }));
      valid = false;
    }

    if (!data?.authorized_signer_title) {
      setDataError((prev) => ({ ...prev, authorized_signer_title: true }));
      valid = false;
    }

    if (!data?.signature_date) {
      setDataError((prev) => ({ ...prev, signature_date: true }));
      valid = false;
    }

    if(!file){
      setFileError(true);
      valid=false;
    }

    if(!file2){
      setFileError2(true);
      valid=false;
    }

    if (!signatureCheck) {
      setDataError((prev) => ({ ...prev, is_signature_checked: true }));
      valid = false;
    }

    //   setDataError((prev)=>({...prev, cobra_admin_details: {
    //     ...prev.cobra_admin_details,
    //     admin_name: true,
    //   },}))
    //   valid=false;
    // }

    if (valid) {
      handleShow();
    }
  };

  const [show, setShow] = useState(false);

  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);

  return (
    <div>
      <div className="form-card">
        <div className="container">
          <div className="form-card-box">
            <div className="form-header">
              <button className="draft-button" onClick={() => handleSubmitData(true)}>Save as Draft</button>
              <h1>Tres Health Group Intake Form</h1>
            </div>

            <form onSubmit={handleSubmit} noValidate>
              <div className="mb-5">
                <div className="mb-3">
                  <div className="form-section-heading">
                    <span className="heading">Billing Details</span>
                  </div>
                  <p>
                    The Tres Health Program requires ACH withdrawals for monthly
                    premium payment. Invoices are typically generated and sent
                    around the 15th of each month for coverage the following
                    month (e.g. invoiced May 15th for June coverage) and
                    withdrawn around the 25th of each month. The first invoice
                    may be off cycle. Please ensure that funds are available by
                    the withdrawal date to avoid interruption of service.
                  </p>
                </div>

                <div className="row">
                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group Legal Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="group_legal_name"
                        id="group_legal_name"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.group_legal_name || ""}
                      ></Form.Control>
                      {dataError?.group_legal_name && (
                        <p className="error">Please enter group legal name</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group Bank Name <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="group_bank_name"
                        id="group_bank_name"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.group_bank_name || ""}
                      ></Form.Control>
                      {dataError?.group_bank_name && (
                        <p className="error">Please enter group bank name</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group Bank Account Number (Checking){" "}
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="group_bank_account_number"
                        id="group_bank_account_number"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.group_bank_account_number || ""}
                      ></Form.Control>
                      {dataError?.group_bank_account_number && (
                        <p className="error">
                          Please enter group bank account number
                        </p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Group ABA Routing Number{" "}
                        <span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="group_aba_routing_number"
                        id="group_aba_routing_number"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.group_aba_routing_number || ""}
                      ></Form.Control>
                      {dataError?.group_aba_routing_number && (
                        <p className="error">
                          Please enter group routing number
                        </p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-5">
                    <p className="fs-7 fw-medium">
                      {" "}
                      <span className="required-star">*</span>
                      General, if this group requires a billing exception
                      additional information may be required.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Group ACH Authorization</span>
                </div>
                <div className="mb-3">
                  <p>
                    I, as an authorized signer of the aforementioned group,
                    hereby authorize Tres Health, Inc. (“Tres”) to initiate
                    monetary withdrawals from my account at the financial
                    institution named above for the limited purposes of payment
                    of amounts due for participation in the Tres Health Program.
                    Such withdraw amount will not exceed the amounts included on
                    the current invoice (or claims funding request, if
                    applicable).
                  </p>
                  <p>
                    Further, I agree not to hold Tres responsible for any delay
                    or loss of funds due to:
                    <span className="fw-medium">
                      (i) incorrect or incomplete information supplied by me or
                      by my financial institution or
                    </span>
                    <span className="fw-medium">
                      (ii) due to an error on the part of my financial
                      institution in depositing funds to my account.
                    </span>
                  </p>

                  <p>
                    This agreement will remain in effect until Tres receives
                    written cancellation from an authorized signer of the group
                    or its financial institution.
                  </p>
                </div>

                <div className="row">
                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Name<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="authorized_signer_name"
                        id="authorized_signer_name"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.authorized_signer_name || ""}
                      ></Form.Control>
                      {dataError?.authorized_signer_name && (
                        <p className="error">Please enter name</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Title<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        name="authorized_signer_title"
                        id="authorized_signer_title"
                        onChange={(e) => {
                          setData((prev: any) => ({
                            ...prev,
                            [e.target.name]: e.target.value,
                          }));
                          setDataError((prev: any) => ({
                            ...prev,
                            [e.target.name]: false,
                          }));
                        }}
                        value={data?.authorized_signer_title || ""}
                      ></Form.Control>
                      {dataError?.authorized_signer_title && (
                        <p className="error">Please enter title</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 mb-3">
                    <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Date<span className="required-star">*</span>
                      </Form.Label>
                      <div className="position-relative date-block">
                        <DatePicker
                          showYearDropdown
                          scrollableYearDropdown
                          yearDropdownItemNumber={100}
                          selected={
                            data?.signature_date
                              ? new Date(data?.signature_date)
                              : null
                          }
                          onChange={(e) => {
                            setData((prev: any) => ({
                              ...prev,
                              signature_date: formatDateToYMD(e),
                            }));
                            setDataError((prev: any) => ({
                              ...prev,
                              signature_date: false,
                            }));
                          }}
                          dateFormat="MM-dd-yyyy"
                          className="form-control"
                          placeholderText="mm-dd-yyyy"
                          autoComplete="off"
                          onKeyDown={(e) => e.preventDefault()}
                        />
                        <i className="calender-icon"></i>
                      </div>
                      {/* <Form.Control type="text"
                      value={data?.signature_date || ''}></Form.Control> */}
                      {dataError?.signature_date && (
                        <p className="error">Please enter date</p>
                      )}
                    </Form.Group>
                  </div>

                  <div className="col-12 mb-2">
                    {/* <Form.Group className="form-block mb-3">
                      <Form.Label>
                        Signature<span className="required-star">*</span>
                      </Form.Label>
                      <Form.Control type="text"></Form.Control>
                    </Form.Group> */}
                    <SignatureFile file={file} setFile={setFile} setFileError={setFileError} />
                    {
                      fileError && (
                        <p className="error">
                          Please sign here
                        </p>
                      )
                    }
                  </div>

                  <div className="col-12 col-md-9">
                    <Form.Check
                      type="checkbox"
                      label="Please check here if a billing arrangement exception was previously granted by Tres Health and, therefore, the bank account information is not required at this time. Please note that if you check off this box and an exception was not granted, implementation will be delayed."
                      onChange={(e: any) => {
                        setSignatuteCheck(e.target.checked);
                        setDataError((prev: any) => ({
                          ...prev,
                          is_signature_checked: false,
                        }));
                      }}
                    />
                    {dataError?.is_signature_checked && (
                      <p className="error">Please check</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-5">
                <div className="form-section-heading">
                  <span className="heading">Attachments</span>
                </div>

                <div>
                  <span className="fs-6 fw-medium mb-1">
                    Signed Proposal or Renewal{" "}
                    <span className="required-star">*</span>
                  </span>
                  <div className="file-box mb-3 col-12 col-md-6">
                    <div className="attach-box d-flex align-items-center justify-content-center">
                      Attach File
                      <i className="file-icon"></i>
                    </div>
                    {file2?.name ? file2?.name : "No Files Attached"}
                    <Form.Group
                      controlId="formFile"
                      className="mb-3 col-12 col-md-6 main-attach-element"
                    >
                      <Form.Control
                        type="file"
                        name="signed_proposal_file_name"
                        id="signed_proposal_file_name"
                        onChange={handleFileChange2} 
                        accept=".pdf, .doc, .docx, .xls, .xlsx"
                      />
                    </Form.Group>
                  </div>
                  {fileError2 && (
                    <p className="error">
                      Please upload the signed proposal or renewal{" "}
                    </p>
                  )}
                </div>
              </div>

              {/* --------------- submit-button----------------- */}

              <div className="button-block">
                <button
                  type="button"
                  className="transparent-btn"
                  onClick={() => setStep("3")}
                >
                  <i className="left-arrow"></i>
                  Back
                </button>

                <button type="submit" className="purple-btn">
                  Save & Complete <i className="right-arrow"></i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* --------------------modal------------------------------- */}

      <Modal show={show} onHide={handleClose}>
        <Modal.Header closeButton>
          <span className="icon-block">
            <i className="question-icon"></i>
          </span>
        </Modal.Header>
        <Modal.Body>Are you Sure? You want to Submit the Data?</Modal.Body>
        <Modal.Footer>
          <div className="row  justify-content-center">
            <div className="col-sm-6 mb-3 mb-sm-0 d-flex align-items-center justify-content-center">
              <button
                type="button"
                className="transparent-btn"
                onClick={() => setStep("1")}
              >
                Cancel
              </button>
            </div>

            <div className="col-sm-6 d-flex align-items-center justify-content-center">
              <button
                type="submit"
                className="purple-btn"
                onClick={()=>handleSubmitData(false)}
              >
                Submit
              </button>
            </div>
          </div>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default Step4;
