interface HeaderBannerProps {
  step: string;
}

const HeaderBanner: React.FC<HeaderBannerProps> = ({step}) => {
  return (
    <div className='header-banner'>
                    <div className='container'>
                        {(step !== "0" && step !== "completed") && <div className="form-progress-bar">
                            <span className="step-text">Step {step} of 4</span>
                            <div className="step-bar-block">
                                <span className={Number(step) > 0 ? "bar active":"bar"}></span>
                                <span className={Number(step) > 1 ? "bar active":"bar"}></span>
                                <span className={Number(step) > 2 ? "bar active":"bar"}></span>
                                <span className={Number(step) > 3 ? "bar active":"bar"}></span>

                            </div>
                        </div>}
                    </div>
 </div>
  )
}

export default HeaderBanner
