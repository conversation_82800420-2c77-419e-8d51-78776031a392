import React, { useEffect, useRef } from "react";
import SignaturePad from "signature_pad";

interface SignatureFileProps {
  file: Blob | null;
  setFile: (file: Blob | null) => void;
  setFileError: (error: boolean) => void;
}

const SignatureFile: React.FC<SignatureFileProps> = ({file, setFile, setFileError}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const signaturePadRef = useRef<SignaturePad | null>(null);

  console.log("file", file);

  const resizeCanvas = () => {
    const canvas = canvasRef.current!;
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    canvas.width = 500 * ratio;
    canvas.height = 200 * ratio;
    canvas.getContext("2d")!.scale(ratio, ratio);
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    resizeCanvas();

    const sigPad = new SignaturePad(canvas, {
      penColor: "black",
    });

    signaturePadRef.current = sigPad;

    // Use polling or consider a drawEnd event workaround if needed
    const interval = setInterval(() => {
      if (sigPad && !sigPad.isEmpty()) {
        canvas.toBlob((blob) => {
          if (blob) {
            setFile(blob);
            setFileError(false);
          }
        }, "image/png");
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const clearSignature = () => {
    signaturePadRef.current?.clear();
    setFile(null);
    setFileError(false);
  };

  return (
    <div>
      <div className="mb-2">
        Signature<span className="required-star">*</span>
      </div>
      <div className="signature-box flex flex-col items-center gap-4 p-4  w-fit">
        <canvas
          ref={canvasRef}
          className="signature-canvas cursor-crosshair"
          style={{ touchAction: "none" }}
        />
        <button
          onClick={clearSignature}
          className="px-4 py-2 bg-gray-300 rounded d-none"
        >
          Clear
        </button>
      </div>
    </div>
  );
};

export default SignatureFile;
