// SignatureFile.tsx
import React, { useEffect, useRef, useState } from 'react';
import SignaturePad from 'signature_pad';

const SignatureFile: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const signaturePadRef = useRef<SignaturePad | null>(null);
  const [signatureDataUrl, setSignatureDataUrl] = useState<string | null>(null);

  console.log("signatureDataUrl",signatureDataUrl)

  // Auto resize canvas for high-DPI screens
  const resizeCanvas = () => {
    const canvas = canvasRef.current!;
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    canvas.width = 500 * ratio;
    canvas.height = 200 * ratio;
    canvas.getContext('2d')!.scale(ratio, ratio);
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    resizeCanvas();

    const sigPad = new SignaturePad(canvas, {
      penColor: 'black',
    });

    signaturePadRef.current = sigPad;

    // Attach signature change handler using polling (as workaround)
    const interval = setInterval(() => {
      if (sigPad && !sigPad.isEmpty()) {
        const dataUrl = sigPad.toDataURL('image/png');
        setSignatureDataUrl(dataUrl);
      }
    }, 1000); // Check every second while drawing

    return () => {
      clearInterval(interval);
    };
  }, []);

  const clearSignature = () => {
    signaturePadRef.current?.clear();
    setSignatureDataUrl(null);
  };

  return (
    <div className="flex flex-col items-center gap-4 p-4 border rounded-md w-fit">
      <canvas
        ref={canvasRef}
        className="border rounded cursor-crosshair bg-white"
        width={500}
        height={200}
        style={{ touchAction: 'none' }}
      />
      <button
        onClick={clearSignature}
        className="px-4 py-2 bg-gray-300 rounded"
      >
        Clear
      </button>
    </div>
  );
};

export default SignatureFile;
