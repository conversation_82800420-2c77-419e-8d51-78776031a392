import { useState, useEffect } from "react"
import FormsWarp from "../components/FormsWrap/FormsWarp"
import HeaderBanner from "../components/Header/HeaderBanner/HeaderBanner"
import Step0 from "../components/FormsWrap/Step0/Step0";
import endPoints from "../api/endpoints";
import { axiosInstance } from "../api/axios";
import { notify } from "../utils/NotifyToasts";
import axios from "axios";
import { decryptionObj, encryption } from "../utils/Encrypt";
import { useParams, useSearchParams } from "react-router-dom";
import LoaderSpin from "../components/LoaderSpin/LoaderSpin";

const Intake = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const {id} = useParams();
  const [validateData, setValidateData] = useState({});
  const [loader, setLoader] = useState(true);
  const [isDraft, setIsDraft] = useState(false);

  // Get step from URL query parameter, default to "0"
  const urlStep = searchParams.get("step") || "0";

  // Validate step value (ensure it's a valid step)
  const validSteps = ["0", "1", "2", "3", "4", "completed"];
  const step = validSteps.includes(urlStep) ? urlStep : "0";

  // Function to update step in both state and URL
  const setStep = (newStep: string) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set("step", newStep);
      return newParams;
    });
  };

  // Initialize URL with step parameter if not present or invalid
  useEffect(() => {
    if (!searchParams.has("step") || !validSteps.includes(urlStep)) {
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set("step", "0");
        return newParams;
      });
    }
  }, [searchParams, setSearchParams, urlStep, validSteps]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [step]);

  useEffect(()=>{
    if(id){
      validateKey();
    }
  }, [id])

  const validateKey = async () =>{
    const EncKey = await encryption(id || "");
    setLoader(true);
    axiosInstance
      .post(`${endPoints.validate}?group_key=${encodeURIComponent(EncKey || "")}`)
      .then(async (response) => {
        setLoader(false);
        if (response?.data?.code === 200) {
          const decryptedData = await decryptionObj(response?.data?.response);
                    if(decryptedData){
                      setValidateData(JSON.parse(decryptedData));
                    }
        }
        else{
          notify.errorToast(
            response?.data?.response ?? "Something went wrong!"
          );
        }
      })
      .catch((error) => {
        setLoader(false);
        if (axios.isAxiosError(error)) {
          notify.errorToast(
            error.response?.data?.response ?? "Something went wrong!"
          );
        }
      });
  }

  return (
    <div>
      {
        loader && <LoaderSpin/>
      }
        <HeaderBanner step={step}/>
        {
          step === "0" ? <Step0 setIsDraft={setIsDraft} setLoader={setLoader} validateData={validateData} setStep={setStep}/> : <FormsWarp isDraft={isDraft} setLoader={setLoader} step={step} setStep={setStep}/>
        }
    </div>
  )
}

export default Intake
