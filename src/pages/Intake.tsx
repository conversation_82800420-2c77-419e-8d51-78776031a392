import { useState } from "react"
import FormsWarp from "../components/FormsWrap/FormsWarp"
import HeaderBanner from "../components/Header/HeaderBanner/HeaderBanner"
import Step0 from "@/components/FormsWrap/Step0/Step0";

const Intake = () => {
  const [step, setStep]= useState("0");
  return (
    <div>
        <HeaderBanner step={step}/>
        {
          step === "0" ? <> <Step0/> </> : <FormsWarp step={step} setStep={setStep}/>
        }
    </div>
  )
}

export default Intake
