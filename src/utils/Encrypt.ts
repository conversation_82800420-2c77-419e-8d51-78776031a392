import CryptoJS from "crypto-js";

const i_vector: string = import.meta.env.VITE_ENCRYPT_ID || '';

// --------encrypt single string--------//
export const encryption = async (msg: string): Promise<string | undefined> => {
  try {
    const secretKey: string | undefined = import.meta.env.VITE_ENCRYPT_KEY;
    if (!secretKey) throw new Error('Encryption key not found.');

    let key = CryptoJS.enc.Utf8.parse(secretKey);
    let iv = CryptoJS.enc.Utf8.parse(i_vector);
    let encryptedCP = CryptoJS.AES.encrypt(msg, key, { iv: iv });
    let cryptText = encryptedCP.toString();

    return cryptText;
  } catch (error) {
    console.error('Encryption error:', error);
    return undefined;
  }
};

// --------decrypt single string--------
export const decryption = async (msg: string): Promise<string | undefined> => {
  try {
    const secretKey: string | undefined = import.meta.env.VITE_ENCRYPT_KEY;
    if (!secretKey) throw new Error('Decryption key not found.');

    let key = CryptoJS.enc.Utf8.parse(secretKey);
    let iv = CryptoJS.enc.Utf8.parse(i_vector);

    let ctx = CryptoJS.enc.Base64.parse(msg);
    let enc = CryptoJS.lib.CipherParams.create({ ciphertext: ctx });
    let decryptText = CryptoJS.AES.decrypt(enc, key, { iv: iv }).toString(
      CryptoJS.enc.Utf8
    );

    return decryptText;
  } catch (err) {
    console.error('Decryption error:', err);
    return undefined;
  }
};

// --------encrypt object--------
export const encryptionObj = async (msg: object): Promise<string | undefined> => {
  try {
    const secretKey: string | undefined = import.meta.env.VITE_ENCRYPT_KEY;
    if (!secretKey) throw new Error('Encryption key not found.');

    let key = CryptoJS.enc.Utf8.parse(secretKey);
    let iv = CryptoJS.enc.Utf8.parse(i_vector);
    let encryptedCP = CryptoJS.AES.encrypt(JSON.stringify(msg), key, {
      iv: iv,
    });
    let cryptText = encryptedCP.toString();
    return cryptText;
  } catch (err) {
    console.error('Object encryption error:', err);
    return undefined;
  }
};

// --------decrypt object--------
export const decryptionObj = async (msg: string): Promise<string | undefined> => {
  try {
    const secretKey: string | undefined = import.meta.env.VITE_ENCRYPT_KEY;
    if (!secretKey) throw new Error('Decryption key not found.');

    let key = CryptoJS.enc.Utf8.parse(secretKey);
    let iv = CryptoJS.enc.Utf8.parse(i_vector);
    let ctx = CryptoJS.enc.Base64.parse(msg);
    let enc = CryptoJS.lib.CipherParams.create({ ciphertext: ctx });
    return CryptoJS.AES.decrypt(enc, key, { iv: iv }).toString(
      CryptoJS.enc.Utf8
    );
  } catch (err) {
    console.error('Object decryption error:', err);
    return undefined;
  }
};
