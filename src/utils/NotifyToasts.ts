import { toast } from "react-toastify";

// Close any visible toast before showing a new one
const closePreviousToast = () => {
  toast.dismiss();
};

const notifyToast = (message: string) => {
  closePreviousToast();
  toast(message, {
    theme: "dark",
    autoClose: 5000,
    hideProgressBar: false,
    pauseOnHover: false,
    pauseOnFocusLoss: false,
    position: "top-right",
  });
};

const successToast = (message: string) => {
  closePreviousToast();
  toast.success(message, {
    theme: "dark",
    autoClose: 5000,
    hideProgressBar: false,
    pauseOnHover: false,
    pauseOnFocusLoss: false,
    position: "top-right",
  });
};

const errorToast = (message: string) => {
  closePreviousToast();
  toast.error(message, {
    theme: "dark",
    autoClose: 5000,
    hideProgressBar: false,
    pauseOnHover: false,
    pauseOnFocusLoss: false,
    position: "top-right",
  });
};

const warningToast = (message: string) => {
  closePreviousToast();
  toast(message, {
    theme: "dark",
    autoClose: 5000,
    hideProgressBar: false,
    pauseOnHover: false,
    pauseOnFocusLoss: false,
    position: "top-right",
  });
};

const infoToast = (message: string) => {
  closePreviousToast();
  toast(message, {
    theme: "dark",
    autoClose: 5000,
    hideProgressBar: false,
    pauseOnHover: false,
    pauseOnFocusLoss: false,
    position: "top-right",
  });
};

export const notify = { successToast, errorToast, warningToast, infoToast };

export default notifyToast;
